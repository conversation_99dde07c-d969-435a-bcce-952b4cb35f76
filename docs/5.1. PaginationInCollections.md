# Working with pagination

## Overview

The `Collection` class implements a modern page-based pagination system that replaces the older skip-based approach. This document explains the complete pagination workflow from initialisation to page navigation.

## Key Components

### Page Initialisation

When the a collection is first queried through the menu or any other intent, it is convenient to reset the total page count from the cache. If this is not done, the system will always return the same cached page total. The reset is done by invoking the method `myCollection.resetTotalPages()`, as shown in the example below:

```javascript
// In menu.js
menuCrewUsersTable.onClick = async () => {
  await crewCollection.resetTotalPages(); // Clear any previous pagination state
  state.setField(CREW_SEARCH_QUERY, {}); // Clear any existing search query
  await displayCrewsTable(1); // Load the first page
};
```

### Page Change Handler

The collection responds to page change requests from the UI. The newPage field is informed by the UI and the event onPageChange is called:

```javascript
// In crew.js
crewCollection.onPageChange = async (newPage) => {
  await displayCrewsTable(newPage); // Display the requested page
};
```

### Main Display Function

The function below is an example showing how to count the pages in a collection. You can see how the method `myCollection.countTotalPages({query, limit})` counts the total pages, but there is no need to keep this information within the micro-app since the system keeps it cached in the state.
The query must be called with the currentPage and the system will load the correct data. There is no need to keep the skip anymore and the field is deprecated.

```javascript
// In crew.js
export async function displayCrewsTable(currentPage) {
  // Count total pages based on query and page size
  await crewCollection.countTotalPages({ query, limit: CREWS_PAGE_SIZE });

  // Load data for the current page
  await crewCollection.loadCollectionWithQuery({
    query: { ...query },
    sort: { lastname: 1 },
    collation: { locale: "en" },
    limit: CREWS_PAGE_SIZE,
    currentPage, // Pass the page number directly to the query
  });

  // Send response to the UI
  crewCollection.sendShortResponse(false, null, preserveFieldData);
}
```

### Implementation Notes

1. **Page vs. Skip**: The system now uses page numbers directly instead of calculating skip values:
   - Old approach (deprecated): `skip = pageSize * (pageNumber - 1)`
   - New approach: Pass `currentPage` directly to the query
2. **Total Page Counting**: The `countTotalPages` method calculates the total number of pages based on the query and page size before loading data.
3. **UI Integration**: The UI sends the desired page number in `messageFromUser.content.newPage`, which is then used to load the appropriate data.
4. **Reset Points**: Pagination is reset to the first page when:
   - Initially loading the collection
   - Applying filters
   - Performing searches
   - Clearing searches

This pagination system provides a more intuitive interface where the UI directly specifies which page to display, and the backend handles the conversion to the appropriate database query, improving code readability and maintenance.

NOTE: If the framework UI finds pages.next or pages.previous in the response, it will process pagination in the old style.
