# frontm.ai best practices prompt

## Naming Conventions & Best practices

All variables must use camelCase.
The system uses the latest Javascript ES standards.
The `intentId` arguments must also be camelCase and not magic strings.
Magic strings, numbers, and booleans must be added within constants. If there are system constants within the ALLConstants file, they must be used.
Constants must be created as SCREAMING_SNAKE_CASE.
The terms bot and micro-app and MicroApp mean the same.
Ensure that all variables have a default value when they are created.
Ensure that variables keep always the same data type.
Do not use single quotes.

## Imports

All [frontm.ai](http://frontm.ai) applications import the state from the State core library.

```javascript
import { D, state } from "@frontmltd/frontmjs/core/State";
```

## Main Intent

In every frontm application, there is a file called main.js that implements the `main` intent like this:

```javascript
import { Intent } from "@frontmltd/frontmjs/core/Intent";
import { D, state } from "@frontmltd/frontmjs/core/State";
import { SYSTEM_INTENTS } from "@frontmltd/front/core/ALLConstants";

export let main = Intent.create({
  intentId: SYSTEM_INTENTS.MAIN,
  prompt: "This is the main intent",
  state,
});
main.runOnCloud();
main.onResolution = async () => {
  "Hello world!".sendResponse();
};
```

The main intent runs only when the user starts the application, for instance, responding with a string response, menu, or other type of response.
The main intent doesn't have a matcher. It only matches when the application starts

## Running location

All `Intent` classes can run its code either in the Cloud on on the Edge (mobile phone app or web browser). By default they try to run on the browser unless it is specified to run on the cloud like this:

```javascript
main.runOnCloud();
```

or

```javascript
menuEntry.runOnCloud();
```

or

```javascript
collection.runOnCloud();
```

## The Prompt property

Add a prompt attribute to all classes. For instance:

```javascript
const fileMenu = new MenuEntry("fileMenu", {
  state,
  menu: mainMenu, // Link to parent menu
  label: "File",
  icon: "file-icon",
  color: "#4A86E8",
  order: 1, // Lower numbers appear first,
  prompt: "This menu entry request the users to upload a file",
});
```

## How to implement events

Events must not be passed in the constructor. They must be implemented like this:

```javascript
const fileMenu = new MenuEntry("fileMenu", {
  state,
  menu: mainMenu, // Link to parent menu
  label: "File",
  icon: "file-icon",
  color: "#4A86E8",
  order: 1, // Lower numbers appear first,
  prompt: "This menu entry request the users to upload a file",
});
fileMenu.onClick = async () => {
  "Event called".sendResponse();
};
```

## General configurations

To copnfigure the general behaviour of the application, use the state object implementing the onConfig event:

```javascript
state.onConfig = async () => {
  state.nlp.assistantName = "myCustomAssistantName";
  state.contextAware = true;
};
```

## Other remarks

The default output for [frontm.ai](http://frontm.ai) is like below:

```javascript
D.log({ message: "Console Output", data: myDataObjectToBeShown });
```

[frontm.ai](http://frontm.ai) adds a method to the string objects called sendResponse() and sendEphemeralResponse(). For instance:

```javascript
"My String".sendResponse(); //Which sends a string that gets stored in the conversation history;
"My ephemeral string".sendEphemeralString(); //Displays string but doesn't store within history. This is normally for some welcome or information messages for the users not relevant to user stories.
```

## Typical frontm.ai project structure

# Project Structure

To set up any frontm.ai application, organise your files as follows:

```
your-micro-app/
│
├── src/                   # Source files
│   ├── main.js            # Main intent entry point
│   ├── menu.js            # Menu definition
│   ├── otherFile1.js      # Other functionality
│   └── otherFile2.js      # More functionality
│
├── dist/                  # Build output (generated)
│   └── bundle.js          # Compiled application
│
├── package.json          # Project dependencies and scripts
├── webpack.config.js     # Webpack build configuration
├── .eslintrc.json        # ESLint configuration
├── .prettierrc           # Prettier configuration
├── .gitignore            # Git ignore rules
└── README.md             # Project documentation
```
