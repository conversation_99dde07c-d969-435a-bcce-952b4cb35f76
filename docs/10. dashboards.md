# frontm.js basic dashboards and visualisations

The Dashboard class is used to create and manage dashboard views in the frontm.ai framework.
At this moment the platform integrates MongoDB Atlas Charts dashboards using the Atlas Charts SDK within the web app.
At the moment we are not adding extra UI functionality like drill-downs or live filters. The dashboards and visualisations are static.

```javascript
import { Dashboard } from "@frontmltd/frontmjs/core/Dashboard";
import { state } from "@frontmltd/frontmjs/core/State";

const salesDashboard = new Dashboard("salesDashboard", {
  state,
  title: "Sales Overview",
  preFilter: {
    date: "2024-01-01",
    department: "sales",
  },
  filter: {
    region: "North America",
  },
});
```

## Dashboard Class Constructor

The constructor accepts the following parameters:

- `intentId`: Unique identifier for the dashboard instance.
- `state`: The application state object.
- `title`: The title of the dashboard to be used in the tab.
- `preFilter`: An object containing initial filter values. Pre-filters are usually faster than filters for the response times.
- `filter`: An object containing the current filter values. The final effect is the same as the pre-filters but because they are run after the initial query is run, they tend to be slower
- `prompt`: The prompt for the LLMs documentation.
- `assistant`: The name of the associated LLM for this dashboard.

## How to call a dashboard from an intent

```javascript
import { Intent } from "@frontmltd/frontmjs/core/Intent";
import { Dashboard } from "@frontmltd/frontmjs/core/Dashboard";
import { state } from "@frontmltd/frontmjs/core/State";
import { SYSTEM_INTENTS } from "@frontmltd/frontmjs/core/ALLConstants";

export let main = Intent.create({
  intentId: SYSTEM_INTENTS.MAIN,
  state,
});

main.onResolution = async () => {
  salesDashboard.sendResponse();
};
```
