# Utility Functions

## Table of Contents

- Data Manipulation
  - removeFalsyValFromObject
  - processError
  - validateRequiredFields
- ID Generation
  - getUniqueId
  - getShortUUID
  - generateRandomChar
- Logging
  - addLogEntry
  - addPerfLogEntry
  - logLambdaEvent
  - logLambdaError
  - logActivity
- ElasticSearch
  - addDocToESIndex

## Data Manipulation

### removeFalsyValFromObject

#### Function Signature

```javascript
removeFalsyValFromObject(object);
```

#### Description

Recursively removes falsy values (empty strings, null, undefined, NaN) from an object. This function is useful for cleaning up objects before storing them in a database or sending them over the network, reducing storage requirements and improving data quality.

#### Parameters

| Name   | Type   | Required | Default | Description         |
| ------ | ------ | -------- | ------- | ------------------- |
| object | object | Yes      | \-      | The object to clean |

#### Return Value

- **Type**: Object or null
- **Description**: The cleaned object with falsy values removed, or null if the object is empty after cleaning

#### Usage Examples

##### Basic Example

```javascript
const user = {
  userId: "12345",
  userName: "John Doe",
  middleName: "",
  age: 0,
  address: {
    street: "123 Main St",
    apt: "",
    city: "Anytown",
    state: "CA",
    zipCode: null,
  },
  tags: [],
};

const cleanedUser = removeFalsyValFromObject(user);
// Returns:
// {
//   userId: '12345',
//   userName: 'John Doe',
//   age: 0,
//   address: {
//     street: '123 Main St',
//     city: 'Anytown',
//     state: 'CA'
//   }
// }
```

##### Handling Empty Objects

```javascript
const emptyObject = { a: "", b: null, c: undefined };
const result = removeFalsyValFromObject(emptyObject);
// Returns: null (since all values were falsy)
```

#### Related Functions

- [dbPutItem](http://#dbputitem)
- [validateRequiredFields](http://#validaterequiredfields)

### processError

#### Function Signature

```javascript
processError(promiseError);
```

#### Description

Processes an error from a Promise and formats it for consistent error handling. This function attempts to parse JSON errors and extracts the status code if available, providing a standardized way to handle errors across the application.

#### Parameters

| Name         | Type | Required | Default | Description                               |
| ------------ | ---- | -------- | ------- | ----------------------------------------- |
| promiseError | any  | Yes      | \-      | The error object from a Promise rejection |

#### Return Value

- **Type**: Array<Object>
- **Description**: An array containing a single error object with either the status code or the original error

#### Usage Examples

##### Basic Example

```javascript
try {
  const result = await someAsyncFunction();
  return result;
} catch (error) {
  return processError(error);
}
// If the error has a status code, returns [{ error: statusCode }]
// Otherwise, returns [{ error: originalError }]
```

##### Handling JSON Errors

```javascript
try {
  const response = await fetch("https://api.example.com/data");
  if (!response.ok) {
    throw JSON.stringify({ status: response.status, message: "API error" });
  }
  return await response.json();
} catch (error) {
  return processError(error);
}
// Returns [{ error: responseStatus }] if the error was a JSON string with a status property
```

#### Related Functions

- [addLogEntry](http://#addlogentry)
- [logLambdaError](http://#loglambdaerror)

### validateRequiredFields

#### Function Signature

```javascript
validateRequiredFields(input, requiredFields);
```

#### Description

Validates that an object contains all the required fields with non-empty values. This function is useful for input validation, ensuring that all necessary data is provided before proceeding with an operation.

#### Parameters

| Name           | Type          | Required | Default | Description                            |
| -------------- | ------------- | -------- | ------- | -------------------------------------- |
| input          | object        | Yes      | \-      | The object to validate                 |
| requiredFields | Array<string> | Yes      | \-      | Array of field names that are required |

#### Return Value

- **Type**: String or null
- **Description**: An error message if validation fails, or null if all required fields are present

#### Usage Examples

##### Basic Example

```javascript
const user = {
  userId: "12345",
  userName: "John Doe",
  email: "",
};

const errorMessage = validateRequiredFields(user, [
  "userId",
  "userName",
  "email",
]);
// Returns: "email is a required input" (because email is empty)
```

##### Nested Fields

```javascript
const order = {
  orderId: "ORD-12345",
  customer: {
    id: "12345",
    name: "John Doe",
  },
  items: [{ productId: "P1", quantity: 2 }],
};

const errorMessage = validateRequiredFields(order, [
  "orderId",
  "customer.id",
  "items",
]);
// Returns: null (all required fields are present)
```

##### Handling Numbers

```javascript
const product = {
  productId: "P1",
  name: "Widget",
  price: 0,
  quantity: 10,
};

const errorMessage = validateRequiredFields(product, [
  "productId",
  "name",
  "price",
  "quantity",
]);
// Returns: null (price is 0, but that's a valid number, not empty)
```

#### Related Functions

- [removeFalsyValFromObject](http://#removefalsyvalfromobject)
- [getS3SignedUrl](http://#gets3signedurl)
- [uploadToS3Bucket](http://#uploadtos3bucket)

## ID Generation

### getUniqueId

#### Function Signature

```javascript
getUniqueId();
```

#### Description

Generates a UUID (Universally Unique Identifier) in the format of a standard UUID v4. This function creates a random, unique identifier that can be used for various purposes such as generating primary keys, session IDs, or tracking unique entities.

#### Parameters

None

#### Return Value

- **Type**: String
- **Description**: A UUID v4 string in the format 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'

#### Usage Examples

##### Basic Example

```javascript
const id = getUniqueId();
// Returns something like: "f47ac10b-58cc-4372-a567-0e02b2c3d479"
```

##### Creating a New User

```javascript
const newUser = {
  userId: getUniqueId(),
  userName: "John Doe",
  email: "<EMAIL>",
  createdAt: Date.now(),
};
// The user gets a unique ID like "550e8400-e29b-41d4-a716-************"
```

#### Error Handling

If an error occurs during UUID generation, the function logs the error to the console but doesn't throw an exception.

#### Related Functions

- [getShortUUID](http://#getshortuuid)
- [generateRandomChar](http://#generaterandomchar)

### getShortUUID

#### Function Signature

```javascript
getShortUUID();
```

#### Description

Generates a shorter unique identifier than a standard UUID. This function creates a random string of 22 characters, which is more compact than a UUID while still providing good uniqueness properties, making it suitable for situations where space is a concern.

#### Parameters

None

#### Return Value

- **Type**: String
- **Description**: A 22-character random string

#### Usage Examples

##### Basic Example

```javascript
const id = getShortUUID();
// Returns something like: "a1b2c3d4e5f6g7h8i9j0k1"
```

##### Creating a Short Reference Code

```javascript
const referenceCode = getShortUUID();
// Can be used for short reference codes, tracking IDs, etc.
```

#### Error Handling

If an error occurs during ID generation, the function logs the error to the console but doesn't throw an exception.

#### Related Functions

- [getUniqueId](http://#getuniqueid)
- [generateRandomChar](http://#generaterandomchar)

### generateRandomChar

#### Function Signature

```javascript
generateRandomChar(match);
```

#### Description

Generates a random hexadecimal character, with special handling for UUID version and variant bits. This is a helper function used by getUniqueId to ensure that generated UUIDs conform to the UUID v4 specification.

#### Parameters

| Name  | Type   | Required | Default | Description                         |
| ----- | ------ | -------- | ------- | ----------------------------------- |
| match | string | Yes      | \-      | The character to match ('x' or 'y') |

#### Return Value

- **Type**: String
- **Description**: A single hexadecimal character (0-9, a-f)

#### Usage Examples

##### Basic Example

```javascript
const char = generateRandomChar("x");
// Returns a random hex character (0-9, a-f)
```

##### UUID Version and Variant Bits

```javascript
const versionChar = generateRandomChar("y");
// Returns a hex character with specific bits set for UUID v4 format
```

#### Related Functions

- [getUniqueId](http://#getuniqueid)
- [getShortUUID](http://#getshortuuid)

## Logging

### addLogEntry

#### Function Signature

```javascript
addLogEntry(
  entity,
  ({ level, message, errorCode, data, more } = {}),
  ({ userId, userEmail } = {}),
  ({ userDomain, bot, conversationId } = {}),
);
```

#### Description

Adds a log entry to the system's logging queue. This function provides a standardized way to log events, errors, and other information, with support for various metadata to help with debugging and monitoring.

#### Parameters

| Name                                      | Type             | Required | Default | Description                                   |
| ----------------------------------------- | ---------------- | -------- | ------- | --------------------------------------------- |
| entity                                    | string           | Yes      | \-      | The entity or component generating the log    |
| logInfo                                   | object           | No       | {}      | Log information object                        |
| logInfo.level                             | string           | No       | \-      | Log level (e.g., 'INFO', 'ERROR', 'WARN')     |
| logInfo.message                           | string           | No       | \-      | The log message                               |
| logInfo.errorCode                         | string or number | No       | \-      | Error code if applicable                      |
| [logInfo.data](http://logInfo.data)       | any              | No       | \-      | Additional data to include in the log         |
| logInfo.more                              | object           | No       | \-      | Additional fields to include in the log entry |
| userInfo                                  | object           | No       | {}      | User information object                       |
| userInfo.userId                           | string           | No       | \-      | ID of the user related to the log             |
| userInfo.userEmail                        | string           | No       | \-      | Email of the user related to the log          |
| contextInfo                               | object           | No       | {}      | Context information object                    |
| contextInfo.userDomain                    | string           | No       | \-      | Domain of the user                            |
| [contextInfo.bot](http://contextInfo.bot) | string           | No       | \-      | ID of the bot involved                        |
| contextInfo.conversationId                | string           | No       | \-      | ID of the conversation                        |

#### Return Value

- **Type**: Promise<Object>
- **Description**: The result of sending the message to the SQS queue

#### Usage Examples

##### Basic Example

```javascript
const result = await addLogEntry(
  "UserService",
  { level: "INFO", message: "User logged in successfully" },
  { userId: "user123", userEmail: "<EMAIL>" },
  { userDomain: "example.com" },
);
// Adds an info log entry for a user login
```

##### Error Logging

```javascript
try {
  // Some operation that might fail
} catch (error) {
  const result = await addLogEntry(
    "PaymentService",
    {
      level: "ERROR",
      message: "Payment processing failed",
      errorCode: "PAYMENT_FAILED",
      data: { orderId: "order123", amount: 99.99 },
      more: { errorDetails: error.message, stackTrace: error.stack },
    },
    { userId: "user123", userEmail: "<EMAIL>" },
    { userDomain: "example.com", conversationId: "conv123" },
  );
  // Adds a detailed error log entry
}
```

#### Related Functions

- [addPerfLogEntry](http://#addperflogentry)
- [logLambdaEvent](http://#loglambdaevent)
- [logLambdaError](http://#loglambdaerror)
- [sendMessageToSQSQueue](http://#sendmessagetosqsqueue)

### addPerfLogEntry

#### Function Signature

```javascript
addPerfLogEntry(
  entity,
  ({ message, errorCode, startTime = Date.now(), data, more } = {}),
  ({ userId, userEmail } = {}),
  ({ userDomain, bot, conversationId } = {}),
);
```

#### Description

Adds a performance log entry to the system's logging queue. This function is specifically designed for tracking performance metrics, automatically calculating the duration of operations and conditionally including detailed data based on performance thresholds.

#### Parameters

| Name                                      | Type             | Required | Default      | Description                                   |
| ----------------------------------------- | ---------------- | -------- | ------------ | --------------------------------------------- |
| entity                                    | string           | Yes      | \-           | The entity or component generating the log    |
| perfInfo                                  | object           | No       | {}           | Performance information object                |
| perfInfo.message                          | string           | No       | \-           | The log message                               |
| perfInfo.errorCode                        | string or number | No       | \-           | Error code if applicable                      |
| perfInfo.startTime                        | number           | No       | Current time | The start timestamp of the operation          |
| [perfInfo.data](http://perfInfo.data)     | any              | No       | \-           | Additional data to include in the log         |
| perfInfo.more                             | object           | No       | \-           | Additional fields to include in the log entry |
| userInfo                                  | object           | No       | {}           | User information object                       |
| userInfo.userId                           | string           | No       | \-           | ID of the user related to the log             |
| userInfo.userEmail                        | string           | No       | \-           | Email of the user related to the log          |
| contextInfo                               | object           | No       | {}           | Context information object                    |
| contextInfo.userDomain                    | string           | No       | \-           | Domain of the user                            |
| [contextInfo.bot](http://contextInfo.bot) | string           | No       | \-           | ID of the bot involved                        |
| contextInfo.conversationId                | string           | No       | \-           | ID of the conversation                        |

#### Return Value

- **Type**: Promise<Object>
- **Description**: The result of sending the message to the SQS queue

#### Usage Examples

##### Basic Example

```javascript
const startTime = Date.now();
// Perform some operation
const result = await someOperation();
// Log the performance
const logResult = await addPerfLogEntry(
  "DatabaseService",
  { message: "Query execution", startTime },
  { userId: "user123" },
  { userDomain: "example.com" },
);
// Adds a performance log entry with the duration automatically calculated
```

##### With Additional Data for Slow Operations

```javascript
const startTime = Date.now();
// Perform a potentially slow operation
const result = await complexQuery();
// Log the performance with detailed data
const logResult = await addPerfLogEntry(
  'AnalyticsService',
  {
    message: 'Complex analytics query',
    startTime,
    data: { query: 'SELECT * FROM ...', parameters: { ... } },
    more: { resultCount: result.length }
  },
  { userId: 'user123', userEmail: '<EMAIL>' },
  { userDomain: 'example.com' }
);
// If the operation took longer than PERFORMANCE_THRESHOLD (1000ms),
// the detailed data will be included in the log
```

#### Related Functions

- [addLogEntry](http://#addlogentry)
- [logLambdaEvent](http://#loglambdaevent)
- [logLambdaError](http://#loglambdaerror)

### logLambdaEvent

#### Function Signature

```javascript
logLambdaEvent(event, context, (message = ""));
```

#### Description

Logs an AWS Lambda event with context information. This function is designed for debugging and monitoring Lambda functions, providing a standardized way to log incoming events and their context.

#### Parameters

| Name    | Type   | Required | Default | Description                                 |
| ------- | ------ | -------- | ------- | ------------------------------------------- |
| event   | object | Yes      | \-      | The Lambda event object                     |
| context | object | Yes      | \-      | The Lambda context object                   |
| message | string | No       | ""      | An optional message to include with the log |

#### Return Value

None

#### Usage Examples

##### Basic Example

```javascript
exports.handler = async (event, context) => {
  logLambdaEvent(event, context, "Processing new request");

  // Function implementation

  return { statusCode: 200, body: "Success" };
};
// Logs the Lambda event with context information
```

##### With Custom Message

```javascript
exports.handler = async (event, context) => {
  logLambdaEvent(event, context, "Received API Gateway request");

  // Extract parameters from the event
  const { pathParameters, queryStringParameters, body } = event;

  // Function implementation

  return { statusCode: 200, body: "Success" };
};
// Logs the Lambda event with a custom message
```

#### Related Functions

- [logLambdaError](http://#loglambdaerror)
- [addLogEntry](http://#addlogentry)
- [addPerfLogEntry](http://#addperflogentry)

### logLambdaError

#### Function Signature

```javascript
logLambdaError(error, message);
```

#### Description

Logs an error that occurred in an AWS Lambda function. This function provides a standardized way to log errors in Lambda functions, including the error details and a custom message for context.

#### Parameters

| Name    | Type   | Required | Default | Description                                   |
| ------- | ------ | -------- | ------- | --------------------------------------------- |
| error   | object | Yes      | \-      | The error object                              |
| message | string | Yes      | \-      | A message describing the context of the error |

#### Return Value

None

#### Usage Examples

##### Basic Example

```javascript
exports.handler = async (event, context) => {
  try {
    // Function implementation that might throw an error
    const result = await riskyOperation();
    return { statusCode: 200, body: JSON.stringify(result) };
  } catch (error) {
    logLambdaError(error, "Error during risky operation");
    return { statusCode: 500, body: "Internal Server Error" };
  }
};
// Logs the error with a descriptive message
```

##### With Error Details

```javascript
exports.handler = async (event, context) => {
  try {
    // Parse and validate input
    const input = JSON.parse(event.body);
    if (!input.userId) {
      throw new Error("Missing required field: userId");
    }

    // Function implementation
    return { statusCode: 200, body: "Success" };
  } catch (error) {
    logLambdaError(error, `Error processing request: ${error.message}`);
    return {
      statusCode: error.message.includes("Missing required field") ? 400 : 500,
      body: error.message,
    };
  }
};
// Logs the error with detailed context
```

#### Related Functions

- [logLambdaEvent](http://#loglambdaevent)
- [addLogEntry](http://#addlogentry)
- [processError](http://#processerror)

### logActivity

#### Function Signature

```javascript
logActivity(activity);
```

#### Description

Logs user activity to the activity tracking system. This function filters the activity object to include only relevant fields and sends it to the activity queue for processing and analysis.

#### Parameters

| Name     | Type   | Required | Default | Description                |
| -------- | ------ | -------- | ------- | -------------------------- |
| activity | object | Yes      | \-      | The activity object to log |

#### Return Value

- **Type**: Promise<Object> or undefined
- **Description**: The result of sending the message to the SQS queue, or undefined if the activity object is empty after filtering

#### Usage Examples

##### Basic Example

```javascript
const activity = {
  userId: "user123",
  userEmail: "<EMAIL>",
  domain: "example.com",
  botId: "bot456",
  messageId: "msg789",
  timestamp: Date.now(),
  platform: "web",
  userAgent: "Mozilla/5.0 ...",
  location: "New York",
  // Other activity data
};

const result = await logActivity(activity);
// Logs the user activity to the activity tracking system
```

##### Tracking Bot Interaction

```javascript
const botInteraction = {
  userId: "user123",
  userEmail: "<EMAIL>",
  domain: "example.com",
  domainName: "Example Corp",
  botId: "bot456",
  botName: "Support Bot",
  messageId: "msg789",
  timestamp: Date.now(),
  client: "mobile-app",
  platform: "ios",
  osVersion: "15.0",
  phoneModel: "iPhone 13",
  messageFromUserMetadata: {
    intent: "support_request",
    sentiment: "neutral",
  },
  responsesArray: [{ type: "text", content: "How can I help you today?" }],
};

const result = await logActivity(botInteraction);
// Logs the bot interaction activity
```

#### Related Functions

- [addLogEntry](http://#addlogentry)
- [updateUserLastActivity](http://#updateuserlastactivity)
- [sendMessageToSQSQueue](http://#sendmessagetosqsqueue)

## ElasticSearch

### addDocToESIndex

#### Function Signature

```javascript
addDocToESIndex(doc, index);
```

#### Description

Adds a document to an Elasticsearch index. This function provides a simple way to index documents for search and analytics purposes, abstracting away the details of the Elasticsearch API.

#### Parameters

| Name  | Type   | Required | Default | Description                         |
| ----- | ------ | -------- | ------- | ----------------------------------- |
| doc   | object | Yes      | \-      | The document to index               |
| index | string | Yes      | \-      | The name of the Elasticsearch index |

#### Return Value

- **Type**: Promise<Object> or String
- **Description**: The result of the Lambda invocation, or an error message if the parameters are invalid

#### Usage Examples

##### Basic Example

```javascript
const document = {
  userId: "user123",
  userName: "John Doe",
  email: "<EMAIL>",
  createdAt: Date.now(),
};

const result = await addDocToESIndex(document, "users_index");
// Indexes the user document for search
```

##### Indexing Activity Data

```javascript
const activityData = {
  userId: "user123",
  userEmail: "<EMAIL>",
  userDomain: "example.com",
  operation: "LOGIN",
  timestamp: Date.now(),
  location: "New York",
  device: "iPhone",
  ipAddress: "***********",
};

const result = await addDocToESIndex(activityData, "user_activity_index");
// Indexes the activity data for analytics
```

#### Error Handling

If either the doc or index parameter is missing, the function returns an error message: `Index and doc are mandatory`.

#### Related Functions

- [updateUserLastActivity](http://#updateuserlastactivity)

## Verification Checklist

- ✅ All exported utility functions are documented
- ✅ All parameters are explained
- ✅ All return values are described
- ✅ Examples demonstrate proper usage
- ✅ No sections are incomplete or marked as "TODO"
