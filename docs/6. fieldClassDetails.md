# Field Class in [frontm.ai](http://frontm.ai): Comprehensive Documentation

## Overview

The Field class is a fundamental component of the [frontm.ai](http://frontm.ai) framework's data modelling system. Extending the powerful Intent class architecture, it represents individual data points within documents and serves as the building block for creating interactive UI components that automatically connect to database fields.

Fields are the atomic units of data in the [frontm.ai](http://frontm.ai) architecture, representing everything from simple text inputs to complex interactive components like maps, dropdowns, and file uploads.

## Class Hierarchy and Purpose

The Field class inherits from the Intent class, gaining its messaging and resolution capabilities while adding specialised features for data handling:

```javascript
import { Field } from "@frontmltd/frontmjs/core/Field";
import { FormFieldTypes } from "@frontmltd/frontmjs/core/FormFieldTypes";
import { D, state } from "@frontmltd/frontmjs/core/State";
```

Fields serve multiple purposes within the [frontm.ai](http://frontm.ai) ecosystem:

- Define data structure and types for database storage

- Create UI components for data entry and display

- Implement validation and business logic

- Enable lookup and relationship features between entities

## Field Constructor

The Field constructor accepts a rich set of parameters that control its appearance, behaviour, and functionality:

```javascript
const customerNameField = new Field("customerNameField", {
  // Basic identification and display
  title: "Customer Name",        // Display label
  doc: customerDocLib,           // Parent document
  type: FormFieldTypes.TEXT_FIELD, // Field type

  // Structural properties
  section: customerSection,      // Parent section
  column: 0,                     // Column position in section layout

  // Field constraints and behavior
  primaryKey: false,             // Whether field is primary key
  mandatory: true,               // Required field
  readOnly: false,               // Read-only state
  hidden: false,                 // Hidden from UI
  hiddenInTables: false,         // Hidden in collection tables
  validation: false,             // Validation rules
  includeInQuickEdit: false      // Include fields in pop-ups
  quickView: true                // If fields show as hyperlink in tables

  // Data properties
  value: null,                   // Initial value
  defaultValue: null,            // Default value
  options: ["Option 1", "Option 2"], // For dropdown/multiselect
  dbName: "customerName",        // Database field name
  maxSelectionOptions: 10,       // Max selection in a multi selection field
  temp: true,                    // The field won't be stored in the DB
  fileScope: "conversation",     // "conversation", "domain" or "bot"

  // Appearance customization
  info: "Enter customer name",   // Help text
  minLength: 3,                  // Minimum input length
  maxLength: 100,                // Maximum input length
  height: "200px",               // Field height for text areas
  activeIconUrl: "url image",    // for button type used as icon
  inactiveIconUrl: "url image",  // for button type used as icon

  // Location fields
  pointType: "marker",           // "marker", "route" or "area"
  routeId: "routeId",            // Valid for pointType route
  pointIconUrl: "url to icon",   // url to the icon identifying the marker

  // Lookup functionality
  lookup: false,                 // Lookup field
  lookUpCollection: null,        // Collection for lookup
  lookUpFilter: null,            // Filter for lookup
  valueFromLookUp: null,         // Value field from lookup
  lookUpForeignKey: null,        // Foreign key for lookup

  // Security features
  encrypted: false,              // Encrypted field

  // AI
  prompt: "Reason for this field to exists",

  // State reference
  state,                         // Application state object
});
```

## Field Types

The Field class supports a wide range of field types defined in the FormFieldTypes class:

```javascript
// Basic input types
FormFieldTypes.TEXT_FIELD; // Text input
FormFieldTypes.NUMBER_FIELD; // Numeric input
FormFieldTypes.TEXT_AREA; // Multiline text
FormFieldTypes.RICH_TEXT_AREA; // Formatted text editor
FormFieldTypes.PASSWORD_FIELD; // Password input
FormFieldTypes.EMAIL_FIELD; // Email input
FormFieldTypes.PHONE_NUMBER_FIELD; // Phone number input

// Selection types
FormFieldTypes.CHECKBOX; // Checkbox
FormFieldTypes.RADIOBUTTON; // Radio button
FormFieldTypes.DROPDOWN; // Dropdown selection
FormFieldTypes.MULTI_SELECTION; // Multiple selection
FormFieldTypes.SWITCH; // Toggle switch
FormFieldTypes.SLIDER; // Slider control

// Date and time types
FormFieldTypes.DATE; // Date picker
FormFieldTypes.DATETIME; // Date and time picker
FormFieldTypes.TIME; // Time picker

// Media types
FormFieldTypes.IMAGE_FIELD; // Image upload
FormFieldTypes.VIDEO_FIELD; // Video upload
FormFieldTypes.FILE_FIELD; // File upload

// Special types
FormFieldTypes.LOOKUP; // Lookup field
FormFieldTypes.COORDINATES; // Geographic coordinates
FormFieldTypes.COLOR_FIELD; // Color selection
FormFieldTypes.ALERT_FIELD; // Alert/notification
FormFieldTypes.COLOR_PICKER_FIELD; // Color picker

// Advanced types
FormFieldTypes.MAP_AREA; // Map area selection
FormFieldTypes.OBJECT_DROPDOWN; // Dropdown with objects
FormFieldTypes.OBJECT_LOOKUP; // Lookup with objects
```

## Field Event Handlers

The Field class provides several event handlers for customising behaviour:

```javascript
// Called when the field is initialized
customerNameField.onInit = async (self) => {
  // Set default values or initial state
  if (!self.value) {
    self.value = "New Customer";
  }
};

// Called when focus leaves the field
customerNameField.onMoveOut = async () => {
  // Validate field when user tabs out or clicks elsewhere
  if (self.value.length < 3) {
    self.sendValidationResponse({
      result: false,
      message: "Name must be at least 3 characters",
    });
  }
};

// Called when field is clicked
customerNameField.onClick = async () => {
  // Handle click events
  "Field clicked".sendEphemeralResponse();
};

// Called when search is performed on lookup fields
customerNameField.onSearch = async () => {
  // Implement custom search logic
};

// Called when preparing the field for display
customerNameField.onResponse = async (self) => {
  // Adjust field properties before rendering
  self.readOnly = state.getField("isReadOnlyMode");
};

// Called before the parent document is saved
customerNameField.onSave = async (self) => {
  // Perform validation or transformations before save
  self.value = self.value.trim();
};

// Called when field input is completed
customerNameField.onCompletion = async () => {
  // Handle completion of input
};
```

## Working with Field Values

The Field class provides methods for manipulating field values:

```javascript
// Get the current field value
const currentValue = customerNameField.value;

// Set the field value
customerNameField.value = "Acme Corporation";

// Get the temporary (auto-saved) value
const tempValue = customerNameField.tempValue;

// Reset field to default value
customerNameField.resetProperties();

// Clear auto-saved value
customerNameField.clearAutoSavedValue();
```

## Field Methods for UI Interaction

```javascript
// Send validation response
customerNameField.sendValidationResponse({
  result: false,
  message: "Invalid input",
});

// Send change response
customerNameField.sendChangeResponse();

// Create a message object for UI rendering
const fieldMessage = customerNameField.message();
```

## Field Cloning

Fields can be cloned for reuse in other documents:

```javascript
// Clone a field into another document
const newField = customerNameField.cloneField(newDocInstance);
```

## Lookup Field Functionality

The Field class provides powerful lookup functionality for creating relationships between data entities:

```javascript
// Create a lookup field
const customerTypeField = new Field("customerTypeField", {
  title: "Customer Type",
  doc: customerDocLib,
  type: FormFieldTypes.LOOKUP,
  lookup: true,
  lookUpCollection: customerTypesCollection,
  valueFromLookUp: "typeName",
  lookUpForeignKey: "typeId",
  state,
});

// The lookup logic is handled automatically
customerTypeField.lookupLogic(state);
```

## Field Security and Encryption

Fields support encryption for sensitive data:

```javascript
// Create an encrypted field
const creditCardField = new Field("creditCardField", {
  title: "Credit Card Number",
  doc: customerDocLib,
  type: FormFieldTypes.TEXT_FIELD,
  encrypted: true,
  dbName: "ccNumber",
  state,
});
```

## Example: Complete Field Implementation

Below is a complete example showing how to implement fields within a document:

```javascript
import { Doc } from "@frontmltd/frontmjs/core/Doc";
import { Field } from "@frontmltd/frontmjs/core/Field";
import { Section } from "@frontmltd/frontmjs/core/Section";
import { FormFieldTypes } from "@frontmltd/frontmjs/core/FormFieldTypes";
import { D, state } from "@frontmltd/frontmjs/core/State";

// Create document
const customerDocLib = new Doc("customerDocLib", state, {
  title: "Customer",
  autoSave: true,
});
customerDocLib.runOnCloud();

// Create section
const customerSection = new Section("customerSection", {
  title: "Customer Information",
  doc: customerDocLib,
  columns: 2,
  state,
});

// Create ID field (hidden primary key)
const customerIdField = new Field("customerIdField", {
  title: "Customer ID",
  doc: customerDocLib,
  section: customerSection,
  type: FormFieldTypes.TEXT_FIELD,
  primaryKey: true,
  hidden: true,
  dbName: "customerId",
  column: 0,
  state,
});

// Auto-generate ID on initialization
customerIdField.onInit = async (self) => {
  if (!self.value) {
    self.value = `CUST-${state.getUniqueId()}`;
  }
};

// Create name field with validation
const customerNameField = new Field("customerNameField", {
  title: "Customer Name",
  doc: customerDocLib,
  section: customerSection,
  type: FormFieldTypes.TEXT_FIELD,
  mandatory: true,
  dbName: "customerName",
  column: 0,
  state,
});

// Add validation on leaving field
customerNameField.onMoveOut = async () => {
  if (!customerNameField.value || customerNameField.value.length < 2) {
    customerNameField.sendValidationResponse({
      result: false,
      message: "Customer name must be at least 2 characters",
    });
  }
};

// Create status dropdown
const statusField = new Field("statusField", {
  title: "Status",
  doc: customerDocLib,
  section: customerSection,
  type: FormFieldTypes.DROPDOWN,
  options: ["ACTIVE", "INACTIVE", "PENDING"],
  defaultValue: "ACTIVE",
  column: 1,
  state,
});
```

## Conclusion

The Field class in [frontm.ai](http://frontm.ai) provides a powerful, flexible system for creating data entry components with rich interactive capabilities. Its integration with the Doc and Collection classes enables the construction of complex data models with minimal code, while its extensive event system allows for customising behaviour at various interaction points.

By leveraging Field class features effectively, developers can create sophisticated data entry interfaces that respond intelligently to user actions while maintaining clean, maintainable code. The Field class seamlessly integrates with the rest of the [frontm.ai](http://frontm.ai) architecture to create a cohesive data management system for maritime business applications.
