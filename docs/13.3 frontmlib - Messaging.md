# Messaging

## Table of Contents

- createQueueMsg
- insertMessages
- getMessages
- createTransactionalConversationId
- createOneToOneConversationId
- createIMConversationId
- createConversationId
- sendMessageToSQSQueue

## createQueueMsg

### Function Signature

```javascript
createQueueMsg(userId, userEmail, requestUuid, conversation, message);
```

### Description

Creates a standardized message object for queue processing. This function formats a message with all necessary metadata for delivery, tracking, and processing. It handles different content types and formats, ensuring that messages are properly structured for the messaging system.

### Parameters

| Name         | Type   | Required | Default | Description                                                      |
| ------------ | ------ | -------- | ------- | ---------------------------------------------------------------- |
| userId       | string | Yes      | \-      | ID of the user sending the message                               |
| userEmail    | string | Yes      | \-      | Email of the user sending the message                            |
| requestUuid  | string | Yes      | \-      | Unique identifier for the request                                |
| conversation | object | Yes      | \-      | Conversation object containing conversationId and bot properties |
| message      | object | Yes      | \-      | The message object with content and metadata                     |

### Return Value

- **Type**: Object
- **Description**: A formatted message object ready for queue processing

### Usage Examples

#### Basic Example

```javascript
const queueMessage = createQueueMsg(
  "user123",
  "<EMAIL>",
  "req-uuid-123",
  { conversationId: "conv123", bot: "bot456" },
  {
    messageUuid: "msg123",
    createdOn: Date.now(),
    createdBy: "user123",
    contentType: "100", // Text message
    content: "Hello, world!",
  },
);
// Returns a formatted message object for the queue
```

#### Card Message Example

```javascript
const cardMessage = createQueueMsg(
  "user123",
  "<EMAIL>",
  "req-uuid-456",
  { conversationId: "conv123", bot: "bot456" },
  {
    messageUuid: "msg456",
    createdOn: Date.now(),
    createdBy: "user123",
    contentType: "530", // Card message
    content: [
      { title: "Card 1", description: "Description 1" },
      { title: "Card 2", description: "Description 2" },
    ],
    info: { source: "user interface" },
    options: { displayMode: "carousel" },
  },
);
// Returns a formatted card message object for the queue
```

### Error Handling

If the message contains an error property that is not empty, the function will include this error in the returned queue message instead of the message details.

### Related Functions

- [insertMessages](http://#insertmessages)
- [sendMessageToSQSQueue](http://#sendmessagetosqsqueue)

## insertMessages

### Function Signature

```javascript
async insertMessages(messages)
```

### Description

Inserts multiple messages into the message collection in the database. This function handles the secure storage of messages, including encryption of sensitive content, and manages time-to-live (TTL) fields for message expiration.

### Parameters

| Name     | Type          | Required | Default | Description                        |
| -------- | ------------- | -------- | ------- | ---------------------------------- |
| messages | Array<Object> | Yes      | \-      | Array of message objects to insert |

### Return Value

- **Type**: Object
- **Description**: The response from the database operation, or an error object if the operation failed

### Usage Examples

#### Basic Example

```javascript
const messages = [
  {
    messageId: "msg123",
    conversationId: "conv123",
    createdOn: Date.now(),
    createdBy: "user123",
    contentType: "100",
    content: "Hello, world!",
    ttl: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60, // 30 days from now
  },
  {
    messageId: "msg124",
    conversationId: "conv123",
    createdOn: Date.now() + 1000,
    createdBy: "bot456",
    contentType: "100",
    content: "Hello, user!",
    ttl: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60, // 30 days from now
  },
];

const result = await insertMessages(messages);
// Inserts the messages into the database
```

### Error Handling

If the messages parameter is empty or not an array, the function returns an error object: `{ error: "insertMessages called with empty messages" }`.

If there's a database error, particularly for duplicate messages, the function returns an error object with details:

```javascript
{
  error: "Duplicated messages found",
  data: [/* Array of write errors */]
}
```

### Related Functions

- [getMessages](http://#getmessages)
- [createQueueMsg](http://#createqueuemsg)

## getMessages

### Function Signature

```javascript
async getMessages(conversationId, createdOn, limit = 50, sortOrder = -1, fetchDirection = FETCH_DIRECTION.OLDER)
```

### Description

Retrieves messages from a conversation, with support for pagination and direction control. This function allows you to fetch messages older or newer than a specific timestamp, making it suitable for implementing chat history loading and real-time updates.

### Parameters

| Name           | Type   | Required | Default | Description                                                                                     |
| -------------- | ------ | -------- | ------- | ----------------------------------------------------------------------------------------------- |
| conversationId | string | Yes      | \-      | ID of the conversation to fetch messages from                                                   |
| createdOn      | number | No       | null    | Timestamp to use as reference point for fetching messages                                       |
| limit          | number | No       | 50      | Maximum number of messages to return                                                            |
| sortOrder      | number | No       | \-1     | Sort order: -1 for descending (newest first), 1 for ascending                                   |
| fetchDirection | string | No       | "OLDER" | Direction to fetch: "OLDER" for messages before createdOn, "NEWER" for messages after createdOn |

### Return Value

- **Type**: Array<Object> or Error Object
- **Description**: An array of message objects or an error object if the operation failed

### Usage Examples

#### Basic Example - Get Recent Messages

```javascript
const messages = await getMessages("conv123");
// Returns the 50 most recent messages in the conversation
```

#### Pagination Example - Get Older Messages

```javascript
// Assuming we already have some messages and want to load older ones
const oldestMessageTimestamp = 1623456789000; // Timestamp of the oldest message we have

const olderMessages = await getMessages(
  "conv123",
  oldestMessageTimestamp,
  20,
  -1,
  "OLDER",
);
// Returns up to 20 messages older than the specified timestamp
```

#### Get Newer Messages for Real-time Updates

```javascript
// Assuming we want to check for new messages since the newest one we have
const newestMessageTimestamp = 1623456789000; // Timestamp of the newest message we have

const newerMessages = await getMessages(
  "conv123",
  newestMessageTimestamp,
  10,
  1, // Ascending order for newer messages
  "NEWER",
);
// Returns up to 10 messages newer than the specified timestamp
```

### Error Handling

If the conversationId parameter is empty, the function returns an error object: `{ error: "getMessages called with empty conversationId" }`.

If there's a database error, the function will return the error response from the MongoDB manager.

### Related Functions

- [insertMessages](http://#insertmessages)
- [createQueueMsg](http://#createqueuemsg)

## createTransactionalConversationId

### Function Signature

```javascript
createTransactionalConversationId(transactionId, contextInfo);
```

### Description

Generates a unique conversation ID for transactional conversations. This function creates a deterministic ID based on a transaction identifier and context information, ensuring that the same transaction and context always produce the same conversation ID.

### Parameters

| Name          | Type   | Required | Default | Description                             |
| ------------- | ------ | -------- | ------- | --------------------------------------- |
| transactionId | string | Yes      | \-      | Identifier for the transaction          |
| contextInfo   | object | Yes      | \-      | Context information for the transaction |

### Return Value

- **Type**: String
- **Description**: A unique conversation ID for the transaction

### Usage Examples

#### Basic Example

```javascript
const conversationId = createTransactionalConversationId("order-123456", {
  orderId: "order-123456",
  customerId: "cust-789",
  status: "processing",
});
// Returns a unique conversation ID based on the transaction and context
```

#### Using for Order Updates

```javascript
// When sending order updates, use the same transaction ID and context
// to ensure messages go to the same conversation
const orderUpdatedConversationId = createTransactionalConversationId(
  "order-123456",
  { orderId: "order-123456", customerId: "cust-789", status: "shipped" },
);
// The status has changed, but the conversation ID will be different
// because the contextInfo object is different
```

### Error Handling

If either the transactionId or contextInfo is empty, the function throws an error with the message: `transactionId and contextInfo must not be empty`.

### Related Functions

- [createOneToOneConversationId](http://#createonetoonconversationid)
- [createIMConversationId](http://#createimconversationid)
- [createConversationId](http://#createconversationid)

## createOneToOneConversationId

### Function Signature

```javascript
createOneToOneConversationId(participants);
```

### Description

Generates a unique conversation ID for one-to-one conversations between two participants. This function creates a deterministic ID based on the participant IDs, ensuring that the same two participants always have the same conversation ID regardless of who initiates the conversation.

### Parameters

| Name         | Type          | Required | Default | Description                          |
| ------------ | ------------- | -------- | ------- | ------------------------------------ |
| participants | Array<string> | Yes      | \-      | Array of exactly two participant IDs |

### Return Value

- **Type**: String
- **Description**: A unique conversation ID for the one-to-one conversation

### Usage Examples

#### Basic Example

```javascript
const conversationId = createOneToOneConversationId(["user123", "user456"]);
// Returns a unique conversation ID for these two users
```

#### Order Doesn't Matter

```javascript
const conversationId1 = createOneToOneConversationId(["user123", "user456"]);
const conversationId2 = createOneToOneConversationId(["user456", "user123"]);
// conversationId1 === conversationId2, ensuring the same conversation
// regardless of the order of participants
```

### Error Handling

If the participants array does not contain exactly two non-empty participant IDs, the function throws an error with the message: `two participants required`.

### Related Functions

- [createTransactionalConversationId](http://#createtransactionalconversationid)
- [createIMConversationId](http://#createimconversationid)
- [createConversationId](http://#createconversationid)

## createIMConversationId

### Function Signature

```javascript
createIMConversationId(participants, selectedDomain);
```

### Description

Generates a unique conversation ID for instant messaging conversations involving multiple participants within a specific domain. This function creates a deterministic ID based on the participant IDs and the domain, ensuring that the same group of participants in the same domain always have the same conversation ID.

### Parameters

| Name           | Type          | Required | Default | Description                     |
| -------------- | ------------- | -------- | ------- | ------------------------------- |
| participants   | Array<string> | Yes      | \-      | Array of participant IDs        |
| selectedDomain | string        | Yes      | \-      | The domain for the conversation |

### Return Value

- **Type**: String
- **Description**: A unique conversation ID for the instant messaging conversation

### Usage Examples

#### Basic Example

```javascript
const conversationId = createIMConversationId(
  ["user123", "user456", "user789"],
  "example.com",
);
// Returns a unique conversation ID for these users in this domain
```

#### Order Doesn't Matter

```javascript
const conversationId1 = createIMConversationId(
  ["user123", "user456", "user789"],
  "example.com",
);
const conversationId2 = createIMConversationId(
  ["user789", "user123", "user456"],
  "example.com",
);
// conversationId1 === conversationId2, ensuring the same conversation
// regardless of the order of participants
```

### Error Handling

If the selectedDomain parameter is empty or not provided, the function throws an error with the message: `selectedDomain is required`.

### Related Functions

- [createTransactionalConversationId](http://#createtransactionalconversationid)
- [createOneToOneConversationId](http://#createonetoonconversationid)
- [createConversationId](http://#createconversationid)

## createConversationId

### Function Signature

```javascript
createConversationId(userId, botId);
```

### Description

Generates a unique conversation ID for conversations between a user and a bot. This function creates a deterministic ID based on the user ID and bot ID, ensuring that the same user and bot always have the same conversation ID.

### Parameters

| Name   | Type   | Required | Default | Description    |
| ------ | ------ | -------- | ------- | -------------- |
| userId | string | Yes      | \-      | ID of the user |
| botId  | string | Yes      | \-      | ID of the bot  |

### Return Value

- **Type**: String
- **Description**: A unique conversation ID for the user-bot conversation

### Usage Examples

#### Basic Example

```javascript
const conversationId = createConversationId("user123", "bot456");
// Returns a unique conversation ID for this user and bot
```

#### Order Doesn't Matter

```javascript
const conversationId1 = createConversationId("user123", "bot456");
const conversationId2 = createConversationId("bot456", "user123");
// conversationId1 === conversationId2, ensuring the same conversation
// regardless of the order
```

### Related Functions

- [createTransactionalConversationId](http://#createtransactionalconversationid)
- [createOneToOneConversationId](http://#createonetoonconversationid)
- [createIMConversationId](http://#createimconversationid)

## sendMessageToSQSQueue

### Function Signature

```javascript
async sendMessageToSQSQueue(message, queue, groupId)
```

### Description

Sends a message to an Amazon SQS queue. This function is used for asynchronous processing of messages, allowing different components of the system to communicate through message queues. It supports FIFO queues through the groupId parameter.

### Parameters

| Name    | Type   | Required | Default       | Description                             |
| ------- | ------ | -------- | ------------- | --------------------------------------- |
| message | object | Yes      | \-            | The message object to send to the queue |
| queue   | string | Yes      | \-            | The URL of the SQS queue                |
| groupId | string | No       | Random number | The message group ID for FIFO queues    |

### Return Value

- **Type**: Object or Error Object
- **Description**: The response from the SQS service or an error object if the operation failed

### Usage Examples

#### Basic Example

```javascript
const result = await sendMessageToSQSQueue(
  { type: "notification", userId: "user123", message: "Hello!" },
  "https://sqs.us-east-1.amazonaws.com/123456789012/MyQueue",
  "notifications",
);
// Sends the message to the specified SQS queue
```

#### Using with System Queues

```javascript
const result = await sendMessageToSQSQueue(
  { type: "log", level: "info", message: "User logged in", userId: "user123" },
  LOGS_QUEUE,
  LOGS_GROUP,
);
// Sends a log message to the system logs queue
```

### Error Handling

If the queue parameter is empty, the function logs a warning and returns without sending the message.

If there's an error sending the message to SQS, the function returns an error object:

```javascript
{
  statusCode: 500,
  body: JSON.stringify("Failed to send message.")
}
```

### Related Functions

- [createQueueMsg](http://#createqueuemsg)
- [addLogEntry](http://#addlogentry)
- [logActivity](http://#logactivity)

## Verification Checklist

- ✅ All exported messaging functions are documented
- ✅ All parameters are explained
- ✅ All return values are described
- ✅ Examples demonstrate proper usage
- ✅ No sections are incomplete or marked as "TODO"
