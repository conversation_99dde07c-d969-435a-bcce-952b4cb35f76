# Database Operations

## Table of Contents

- createProjectionExpression
- dbGetWithKey
- dbGetWithIndex
- dbGetWithIndexAndFilter
- dbGetWithPagination
- dbGetBatch
- dbPutItem
- dbUpdateItem
- dbPutBatch
- dbPutMultiBatches
- dbDeleteItem
- getSystemStaticData

## createProjectionExpression

### Function Signature

```javascript
createProjectionExpression(fields);
```

### Description

Creates a projection expression string from an array of field names. This function is used to specify which attributes should be returned in database query results. It converts an array of field names into a comma-separated string that can be used as a projection expression in DynamoDB operations.

### Parameters

| Name   | Type                    | Required | Default | Description                                                             |
| ------ | ----------------------- | -------- | ------- | ----------------------------------------------------------------------- |
| fields | Array<string> or string | Yes      | \-      | An array of field names or a string to be used as projection expression |

### Return Value

- **Type**: String
- **Description**: A comma-separated string of field names

### Usage Examples

#### Basic Example

```javascript
const fields = ["userId", "userName", "emailAddress"];
const projection = createProjectionExpression(fields);
// Result: "userId,userName,emailAddress"
```

#### Using a String

```javascript
const projection = createProjectionExpression("userId, userName");
// Result: "userId, userName"
```

### Related Functions

- [dbGetWithKey](http://#dbgetwithkey)
- [dbGetWithIndex](http://#dbgetwithindex)
- [dbGetWithPagination](http://#dbgetwithpagination)

## dbGetWithKey

### Function Signature

```javascript
async dbGetWithKey(table, filter, fields)
```

### Description

Retrieves an item from a DynamoDB table using a primary key. This function performs a simple get operation on a DynamoDB table, returning a single item that matches the provided key filter. Optionally, you can specify which fields to return using the fields parameter.

### Parameters

| Name   | Type                    | Required | Default | Description                                                         |
| ------ | ----------------------- | -------- | ------- | ------------------------------------------------------------------- |
| table  | string                  | Yes      | \-      | The name of the DynamoDB table                                      |
| filter | object                  | Yes      | \-      | The primary key of the item to retrieve (e.g., `{ userId: "123" }`) |
| fields | Array<string> or string | No       | null    | Fields to include in the result                                     |

### Return Value

- **Type**: Object or Error Object
- **Description**: The retrieved item or an error object with the format `{ error: "Error message" }`

### Usage Examples

#### Basic Example

```javascript
const user = await dbGetWithKey("Users", { userId: "12345" });
// Returns the complete user object with userId '12345'
```

#### With Field Selection

```javascript
const userProfile = await dbGetWithKey("Users", { userId: "12345" }, [
  "userId",
  "userName",
  "emailAddress",
]);
// Returns only the specified fields of the user
```

### Error Handling

If an error occurs during the database operation, the function returns an object with an error property containing the error message:

```javascript
{
  error: "Error in dbGetWithKey with message [error details]";
}
```

### Related Functions

- [dbGetWithIndex](http://#dbgetwithindex)
- [dbGetBatch](http://#dbgetbatch)
- [createProjectionExpression](http://#createprojectionexpression)

## dbGetWithIndex

### Function Signature

```javascript
async dbGetWithIndex(table, indexName, expression, attribValues, options, attributeNames, filterExpression, fields)
```

### Description

Queries a DynamoDB table using a secondary index. This function allows for more complex queries than simple key-based lookups, enabling you to query data using secondary indexes with various conditions and filters.

### Parameters

| Name             | Type                    | Required | Default | Description                                                  |
| ---------------- | ----------------------- | -------- | ------- | ------------------------------------------------------------ |
| table            | string                  | Yes      | \-      | The name of the DynamoDB table                               |
| indexName        | string                  | Yes      | \-      | The name of the secondary index to query                     |
| expression       | string                  | Yes      | \-      | The key condition expression (e.g., "userId = :userId")      |
| attribValues     | object                  | Yes      | \-      | The expression attribute values (e.g., { ":userId": "123" }) |
| options          | object                  | No       | null    | Additional query options like Limit and ScanIndexForward     |
| attributeNames   | object                  | No       | null    | Expression attribute names for reserved keywords             |
| filterExpression | string                  | No       | null    | Filter expression to apply to the results                    |
| fields           | Array<string> or string | No       | null    | Fields to include in the result                              |

### Return Value

- **Type**: Array<Object> or Error Object
- **Description**: An array of items matching the query or an error object with the format `{ error: "Error message" }`

### Usage Examples

#### Basic Example

```javascript
const users = await dbGetWithIndex(
  "Users",
  "emailIndex",
  "emailAddress = :email",
  { ":email": "<EMAIL>" },
);
// Returns all users with the specified email address
```

#### Advanced Example with Options and Filtering

```javascript
const recentOrders = await dbGetWithIndex(
  "Orders",
  "userIdIndex",
  "userId = :userId",
  { ":userId": "12345", ":date": "2023-01-01" },
  { Limit: 10, ScanIndexForward: false },
  { "#date": "date" },
  "#date > :date",
  ["orderId", "orderDate", "totalAmount"],
);
// Returns up to 10 most recent orders for user '12345' after Jan 1, 2023
// with only the specified fields
```

### Error Handling

If an error occurs during the database operation, the function returns an object with an error property containing the error message:

```javascript
{
  error: "Error in dbGetWithIndex with message [error details]";
}
```

### Related Functions

- [dbGetWithKey](http://#dbgetwithkey)
- [dbGetWithIndexAndFilter](http://#dbgetwithindexandfilter)
- [dbGetWithPagination](http://#dbgetwithpagination)

## dbGetWithIndexAndFilter

### Function Signature

```javascript
async dbGetWithIndexAndFilter(table, indexName, keyCondition, filter, attribValues, fields)
```

### Description

Queries a DynamoDB table using a secondary index and applies a filter expression. This function is a simplified version of dbGetWithIndex that specifically focuses on combining index-based queries with filter expressions.

### Parameters

| Name         | Type                    | Required | Default | Description                                             |
| ------------ | ----------------------- | -------- | ------- | ------------------------------------------------------- |
| table        | string                  | Yes      | \-      | The name of the DynamoDB table                          |
| indexName    | string                  | Yes      | \-      | The name of the secondary index to query                |
| keyCondition | string                  | Yes      | \-      | The key condition expression (e.g., "userId = :userId") |
| filter       | string                  | Yes      | \-      | Filter expression to apply to the results               |
| attribValues | object                  | Yes      | \-      | The expression attribute values for both conditions     |
| fields       | Array<string> or string | No       | null    | Fields to include in the result                         |

### Return Value

- **Type**: Array<Object> or Error Object
- **Description**: An array of items matching the query and filter or an error object with the format `{ error: "Error message" }`

### Usage Examples

#### Basic Example

```javascript
const activeUsers = await dbGetWithIndexAndFilter(
  "Users",
  "domainIndex",
  "domain = :domain",
  "status = :status",
  { ":domain": "example.com", ":status": "active" },
  ["userId", "userName", "emailAddress"],
);
// Returns all active users in the example.com domain
```

#### Finding Recent Items

```javascript
const recentPosts = await dbGetWithIndexAndFilter(
  "Posts",
  "authorIndex",
  "authorId = :authorId",
  "createdAt > :timestamp",
  { ":authorId": "12345", ":timestamp": 1672531200000 }, // Jan 1, 2023
  ["postId", "title", "createdAt"],
);
// Returns all posts by author '12345' created after Jan 1, 2023
```

### Error Handling

If an error occurs during the database operation, the function returns an object with an error property containing the error message:

```javascript
{
  error: "Error in dbGetWithIndexAndFilter with message [error details]";
}
```

### Related Functions

- [dbGetWithIndex](http://#dbgetwithindex)
- [dbGetWithPagination](http://#dbgetwithpagination)

## dbGetWithPagination

### Function Signature

```javascript
async dbGetWithPagination(table, indexName, keyCondition, filter, attribValues, exclusiveStartKey, fields, options, attributeNames, sendOnlyItems = true)
```

### Description

Queries a DynamoDB table with pagination support. This function is designed for retrieving large datasets in smaller chunks by supporting pagination through the exclusiveStartKey parameter. It combines the functionality of dbGetWithIndex and dbGetWithIndexAndFilter while adding pagination capabilities.

### Parameters

| Name              | Type                    | Required | Default | Description                                                               |
| ----------------- | ----------------------- | -------- | ------- | ------------------------------------------------------------------------- |
| table             | string                  | Yes      | \-      | The name of the DynamoDB table                                            |
| indexName         | string                  | Yes      | \-      | The name of the secondary index to query                                  |
| keyCondition      | string                  | Yes      | \-      | The key condition expression                                              |
| filter            | string                  | No       | null    | Filter expression to apply to the results                                 |
| attribValues      | object                  | Yes      | \-      | The expression attribute values                                           |
| exclusiveStartKey | object                  | No       | null    | The key to start the query from (for pagination)                          |
| fields            | Array<string> or string | No       | null    | Fields to include in the result                                           |
| options           | object                  | No       | null    | Additional query options like Limit and ScanIndexForward                  |
| attributeNames    | object                  | No       | null    | Expression attribute names for reserved keywords                          |
| sendOnlyItems     | boolean                 | No       | true    | If true, returns only items; if false, returns items and LastEvaluatedKey |

### Return Value

- **Type**: Array<Object> or Object or Error Object
- **Description**:
  - If sendOnlyItems is true: An array of items matching the query
  - If sendOnlyItems is false: An object with format `{ Items: [...], LastEvaluatedKey: {...} }`
  - On error: An error object with the format `{ error: "Error message" }`

### Usage Examples

#### Basic Pagination Example

```javascript
// First query
const firstPage = await dbGetWithPagination(
  "Orders",
  "userIdIndex",
  "userId = :userId",
  null,
  { ":userId": "12345" },
  null,
  ["orderId", "orderDate", "totalAmount"],
  { Limit: 10 },
  null,
  false,
);

// Get next page using LastEvaluatedKey from first query
const secondPage = await dbGetWithPagination(
  "Orders",
  "userIdIndex",
  "userId = :userId",
  null,
  { ":userId": "12345" },
  firstPage.LastEvaluatedKey,
  ["orderId", "orderDate", "totalAmount"],
  { Limit: 10 },
  null,
  false,
);
```

#### Advanced Filtering with Pagination

```javascript
const recentOrders = await dbGetWithPagination(
  "Orders",
  "userIdIndex",
  "userId = :userId",
  "orderDate > :date",
  { ":userId": "12345", ":date": "2023-01-01" },
  null,
  ["orderId", "orderDate", "totalAmount"],
  { Limit: 20, ScanIndexForward: false },
  { "#date": "date" },
);
// Returns up to 20 most recent orders for user '12345' after Jan 1, 2023
```

### Error Handling

If an error occurs during the database operation, the function returns an object with an error property containing the error message:

```javascript
{
  error: "Error in dbGetWithPagination with message [error details]";
}
```

### Related Functions

- [dbGetWithIndex](http://#dbgetwithindex)
- [dbGetWithIndexAndFilter](http://#dbgetwithindexandfilter)

## dbGetBatch

### Function Signature

```javascript
async dbGetBatch(table, filterList, fields)
```

### Description

Retrieves multiple items from a DynamoDB table in a single batch operation. This function allows you to efficiently fetch multiple items from a table by providing a list of primary keys, reducing the number of round trips to the database.

### Parameters

| Name       | Type                    | Required | Default | Description                          |
| ---------- | ----------------------- | -------- | ------- | ------------------------------------ |
| table      | string                  | Yes      | \-      | The name of the DynamoDB table       |
| filterList | Array<Object>           | Yes      | \-      | An array of primary keys to retrieve |
| fields     | Array<string> or string | No       | null    | Fields to include in the result      |

### Return Value

- **Type**: Object or Error Object
- **Description**: An object with table name as key and array of items as value, or an error object with the format `{ error: "Error message" }`

### Usage Examples

#### Basic Example

```javascript
const userKeys = [
  { userId: "12345" },
  { userId: "67890" },
  { userId: "abcde" },
];

const users = await dbGetBatch("Users", userKeys);
// Returns an object like: { "Users": [user1, user2, user3] }
```

#### With Field Selection

```javascript
const userKeys = [{ userId: "12345" }, { userId: "67890" }];

const userProfiles = await dbGetBatch("Users", userKeys, [
  "userId",
  "userName",
  "emailAddress",
]);
// Returns only the specified fields for each user
```

### Error Handling

If an error occurs during the database operation, the function returns an object with an error property containing the error message:

```javascript
{
  error: "Error in dbGetBatch with message [error details]";
}
```

### Related Functions

- [dbGetWithKey](http://#dbgetwithkey)
- [dbPutBatch](http://#dbputbatch)

## dbPutItem

### Function Signature

```javascript
async dbPutItem(table, document)
```

### Description

Inserts or replaces an item in a DynamoDB table. This function writes a complete item to the specified table, overwriting any existing item with the same primary key. Before writing, it removes any falsy values from the document to optimize storage.

### Parameters

| Name     | Type   | Required | Default | Description                    |
| -------- | ------ | -------- | ------- | ------------------------------ |
| table    | string | Yes      | \-      | The name of the DynamoDB table |
| document | object | Yes      | \-      | The item to insert or replace  |

### Return Value

- **Type**: Object or Error Object
- **Description**: The response from DynamoDB or an error object with the format `{ error: "Error message" }`

### Usage Examples

#### Basic Example

```javascript
const user = {
  userId: "12345",
  userName: "John Doe",
  emailAddress: "<EMAIL>",
  createdAt: Date.now(),
};

const result = await dbPutItem("Users", user);
// Inserts or replaces the user in the Users table
```

#### With Nested Objects

```javascript
const order = {
  orderId: "ORD-12345",
  userId: "12345",
  items: [
    { productId: "P1", quantity: 2, price: 10.99 },
    { productId: "P2", quantity: 1, price: 24.99 },
  ],
  shippingAddress: {
    street: "123 Main St",
    city: "Anytown",
    zipCode: "12345",
  },
  orderDate: Date.now(),
  status: "pending",
};

const result = await dbPutItem("Orders", order);
// Inserts or replaces the order in the Orders table
```

### Error Handling

If an error occurs during the database operation, the function returns an object with an error property containing the error message:

```javascript
{
  error: "Error in dbPutItem with message [error details]";
}
```

### Related Functions

- [dbUpdateItem](http://#dbupdateitem)
- [dbPutBatch](http://#dbputbatch)
- [removeFalsyValFromObject](http://#removefalsyvalfromobject)

## dbUpdateItem

### Function Signature

```javascript
async dbUpdateItem(table, filter, expression, attribValues, attribNames, conditionExpression)
```

### Description

Updates specific attributes of an existing item in a DynamoDB table. This function allows for partial updates to an item without replacing the entire item, using update expressions to specify which attributes to modify and how.

### Parameters

| Name                | Type   | Required | Default | Description                                                     |
| ------------------- | ------ | -------- | ------- | --------------------------------------------------------------- |
| table               | string | Yes      | \-      | The name of the DynamoDB table                                  |
| filter              | object | Yes      | \-      | The primary key of the item to update                           |
| expression          | string | Yes      | \-      | The update expression (e.g., "SET #name = :name")               |
| attribValues        | object | Yes      | \-      | The expression attribute values (e.g., { ":name": "New Name" }) |
| attribNames         | object | No       | null    | Expression attribute names for reserved keywords                |
| conditionExpression | string | No       | null    | Condition that must be satisfied for the update to occur        |

### Return Value

- **Type**: Object or Error Object
- **Description**: The response from DynamoDB or an error object with the format `{ error: "Error message" }`

### Usage Examples

#### Basic Example

```javascript
const result = await dbUpdateItem(
  "Users",
  { userId: "12345" },
  "SET userName = :name, lastUpdated = :time",
  { ":name": "Jane Doe", ":time": Date.now() },
);
// Updates the userName and lastUpdated attributes for the user
```

#### With Attribute Names and Condition Expression

```javascript
const result = await dbUpdateItem(
  "Products",
  { productId: "P12345" },
  "SET #price = :newPrice, #stock = #stock - :quantity",
  { ":newPrice": 19.99, ":quantity": 1, ":currentStock": 0 },
  { "#price": "price", "#stock": "stockQuantity" },
  "#stock > :currentStock",
);
// Updates the price and decrements the stock quantity,
// but only if the current stock is greater than 0
```

### Error Handling

If an error occurs during the database operation, the function returns an object with an error property containing the error message:

```javascript
{
  error: "Error in dbUpdateItem with message [error details]";
}
```

### Related Functions

- [dbPutItem](http://#dbputitem)
- [dbGetWithKey](http://#dbgetwithkey)

## dbPutBatch

### Function Signature

```javascript
async dbPutBatch(table, putRequests)
```

### Description

Writes multiple items to a DynamoDB table in a single batch operation. This function allows you to efficiently insert or replace multiple items in a table at once, reducing the number of round trips to the database.

### Parameters

| Name        | Type          | Required | Default | Description                                                |
| ----------- | ------------- | -------- | ------- | ---------------------------------------------------------- |
| table       | string        | Yes      | \-      | The name of the DynamoDB table                             |
| putRequests | Array<Object> | Yes      | \-      | An array of put requests, each containing an Item property |

### Return Value

- **Type**: Object or Error Object
- **Description**: The response from DynamoDB or an error object with the format `{ error: "Error message" }`

### Usage Examples

#### Basic Example

```javascript
const users = [
  {
    Item: {
      userId: "12345",
      userName: "John Doe",
      emailAddress: "<EMAIL>",
    },
  },
  {
    Item: {
      userId: "67890",
      userName: "Jane Smith",
      emailAddress: "<EMAIL>",
    },
  },
];

const result = await dbPutBatch("Users", users);
// Inserts or replaces both users in the Users table
```

#### Creating Put Requests from Objects

```javascript
const products = [
  { productId: "P1", name: "Product 1", price: 10.99 },
  { productId: "P2", name: "Product 2", price: 24.99 },
];

const putRequests = products.map((product) => ({ Item: product }));
const result = await dbPutBatch("Products", putRequests);
// Inserts or replaces both products in the Products table
```

### Error Handling

If an error occurs during the database operation, the function returns an object with an error property containing the error message:

```javascript
{
  error: "Error in dbPutBatch with message [error details]";
}
```

### Related Functions

- [dbPutItem](http://#dbputitem)
- [dbPutMultiBatches](http://#dbputmultibatches)
- [dbGetBatch](http://#dbgetbatch)

## dbPutMultiBatches

### Function Signature

```javascript
async dbPutMultiBatches(putRequests)
```

### Description

Writes multiple items to multiple DynamoDB tables in a single batch operation. This function extends the functionality of dbPutBatch to support writing to multiple tables at once, providing even greater efficiency for complex operations.

### Parameters

| Name        | Type   | Required | Default | Description                                                             |
| ----------- | ------ | -------- | ------- | ----------------------------------------------------------------------- |
| putRequests | object | Yes      | \-      | An object with table names as keys and arrays of put requests as values |

### Return Value

- **Type**: Object
- **Description**: The response from DynamoDB

### Usage Examples

#### Basic Example

```javascript
const batchRequests = {
  Users: [
    { Item: { userId: "12345", userName: "John Doe" } },
    { Item: { userId: "67890", userName: "Jane Smith" } },
  ],
  UserProfiles: [
    {
      Item: {
        userId: "12345",
        bio: "Software Developer",
        location: "New York",
      },
    },
    {
      Item: {
        userId: "67890",
        bio: "Product Manager",
        location: "San Francisco",
      },
    },
  ],
};

const result = await dbPutMultiBatches(batchRequests);
// Inserts or replaces items in both the Users and UserProfiles tables
```

#### Complex Example with Different Operations

```javascript
const batchRequests = {
  Orders: [
    { Item: { orderId: "O1", userId: "12345", status: "pending" } },
    { Item: { orderId: "O2", userId: "67890", status: "shipped" } },
  ],
  OrderItems: [
    { Item: { orderId: "O1", itemId: "I1", productId: "P1", quantity: 2 } },
    { Item: { orderId: "O1", itemId: "I2", productId: "P2", quantity: 1 } },
    { Item: { orderId: "O2", itemId: "I3", productId: "P3", quantity: 3 } },
  ],
};

const result = await dbPutMultiBatches(batchRequests);
// Inserts or replaces items in both the Orders and OrderItems tables
```

### Related Functions

- [dbPutBatch](http://#dbputbatch)
- [dbPutItem](http://#dbputitem)

## dbDeleteItem

### Function Signature

```javascript
async dbDeleteItem(collection, key)
```

### Description

Deletes an item from a DynamoDB table. This function removes a single item from the specified table based on its primary key.

### Parameters

| Name       | Type   | Required | Default | Description                           |
| ---------- | ------ | -------- | ------- | ------------------------------------- |
| collection | string | Yes      | \-      | The name of the DynamoDB table        |
| key        | object | Yes      | \-      | The primary key of the item to delete |

### Return Value

- **Type**: Object
- **Description**: The response from DynamoDB

### Usage Examples

#### Basic Example

```javascript
const result = await dbDeleteItem("Users", { userId: "12345" });
// Deletes the user with userId '12345' from the Users table
```

#### Composite Key Example

```javascript
const result = await dbDeleteItem("OrderItems", {
  orderId: "O1",
  itemId: "I1",
});
// Deletes the order item with the specified composite key
```

### Related Functions

- [dbPutItem](http://#dbputitem)
- [dbUpdateItem](http://#dbupdateitem)

## getSystemStaticData

### Function Signature

```javascript
async getSystemStaticData(key, encryptedValue = false)
```

### Description

Retrieves static data from the system's key-value store. This function is used to fetch configuration or reference data that is stored in a central location. It can optionally handle encrypted values.

### Parameters

| Name           | Type    | Required | Default | Description                                              |
| -------------- | ------- | -------- | ------- | -------------------------------------------------------- |
| key            | string  | Yes      | \-      | The key of the data to retrieve                          |
| encryptedValue | boolean | No       | false   | Whether the value is encrypted and needs to be decrypted |

### Return Value

- **Type**: Object
- **Description**: The retrieved data object or null if not found

### Usage Examples

#### Basic Example

```javascript
const config = await getSystemStaticData("appConfig");
// Returns the application configuration
```

#### Retrieving Encrypted Data

```javascript
const apiKeys = await getSystemStaticData("apiKeys", true);
// Returns the decrypted API keys
```

### Related Functions

- [encryptData](http://#encryptdata)
- [decryptData](http://#decryptdata)

## Verification Checklist

- ✅ All exported database functions are documented
- ✅ All parameters are explained
- ✅ All return values are described
- ✅ Examples demonstrate proper usage
- ✅ No sections are incomplete or marked as "TODO"
