# Container Management in frontm.ai: A Comprehensive Guide

The frontm.ai framework provides sophisticated container management capabilities for building complex, multi-component user interfaces. This document explores the Container class, which serves as a foundational component for orchestrating multiple documents, collections, and other UI elements within a unified interface.

## Overview of Container Management

frontm.ai employs a unified architecture where containers act as orchestrators that manage and coordinate multiple child components. The `Container` class extends the `Intent` class, inheriting its messaging and resolution capabilities while adding specialized features for managing complex UI compositions.

Containers enable developers to create sophisticated interfaces that combine forms, tables, and other components while maintaining consistent behavior, auto-save functionality, and coordinated user interactions across all child elements.

## Core Container Class: In-Depth Examination

The `Container` class is a foundational component in frontm.ai's UI orchestration system. It represents a composite interface that can contain multiple documents, collections, forms, and tables, providing a unified experience for complex business operations.

### Container Class Constructor in Detail

The `Container` constructor accepts extensive parameters that control its behavior, appearance, and child management:

```javascript
import { Container } from "@frontmltd/frontmjs/core/Container";
import { D, state } from "@frontmltd/frontmjs/core/State";

export let customerManagementContainer = new Container(
  "customerManagementContainer",
  {
    // Basic display properties
    tabId: "customerTab", // Tab identifier for the container
    title: "Customer Management", // Display title for the container
    description: "Manage customer information and related data", // Description

    // Container behavior controls
    allowMinimize: true, // Allow container to be minimized
    allowClose: true, // Allow container to be closed
    minimizeOnConfirm: false, // Minimize after confirmation
    promptOnClose: false, // Prompt user before closing
    updateInBackground: false, // Update without showing UI changes

    // Action button configuration
    confirm: "Save All", // Label for confirm button
    cancel: "Cancel", // Label for cancel button
    more: ["Export", "Import", "Archive"], // Additional action buttons

    // UI layout and styling
    style: CONTAINER_CONSTANTS.STYLE.CONTAINER, // Container style

    // AI integration
    assistant: "customer-assistant", // AI assistant identifier
    prompt: "Container for managing customer data and operations",
    dynamic: true, // Enable dynamic content
    summaryOnResponse: true, // Include summary in responses
    summarisationPrompt: "Summarize customer management operations",

    state, // Application state reference
  },
);
```

### Container Event Handlers

The `Container` class provides several event handlers for managing complex interactions:

```javascript
// Called when the container is closed
customerManagementContainer.onClose = async () => {
  // Perform cleanup operations
  await saveAllPendingChanges();
  await logContainerClosure();
};

// Called when the container is submitted/confirmed
customerManagementContainer.onSubmit = async () => {
  // Process all child components
  await saveAllDocuments();
  await updateCollections();
  "All changes saved successfully".sendResponse();
};

// Called when the container operations are cancelled
customerManagementContainer.onCancel = async () => {
  // Revert any pending changes
  await revertAllChanges();
  "Changes cancelled".sendResponse();
};

// Called when additional action buttons are clicked
customerManagementContainer.onMoreButtons = async () => {
  // Handle custom actions based on which button was clicked
  const action = state.messageFromUser.action;
  switch (action) {
    case "Export":
      await exportCustomerData();
      break;
    case "Import":
      await importCustomerData();
      break;
    case "Archive":
      await archiveCustomerData();
      break;
  }
};
```

### Managing Child Components

The `Container` class provides comprehensive methods for managing child components. The child components are stored in an array and they are shown in the UI in the same order as they are stored in this array:

```javascript
// Add a document as a child
customerManagementContainer.addChild(customerDocLib);

// Add a collection as a child
customerManagementContainer.addChild(customersCollectionLib);

// Insert a child at a specific position
customerManagementContainer.insertChild(0, priorityDocLib);

// Remove a child component
customerManagementContainer.removeChild(customerDocLib);
```

### Adding Forms and Tables

The container can explicitly manage forms and tables:

### Container Configuration Options

The container provides extensive configuration through its options:

### User Interface Integration (Container responses)

As all the componentents in frontm.ai, the containers implements the sendResponse method that is sent to the UIs to render the UI:

```javascript
// Send full container response
customerManagementContainer.sendResponse();

// Send optimized short response (for mobile/performance)
customerManagementContainer.sendShortResponse(
  state, // Application state
  false, // Skip onResponse
  preserveData, // Data to preserve between updates
);
```

## Building a Container: Step-by-Step Guide

### 1. Creating a Basic Container

Start by defining a container class that will orchestrate your components:

```javascript
import { Container } from "@frontmltd/frontmjs/core/Container";
import { D, state } from "@frontmltd/frontmjs/core/State";

export let customerManagementContainer = new Container(
  "customerManagementContainer",
  {
    title: "Customer Management",
    description: "Comprehensive customer management interface",
    allowMinimize: true,
    allowClose: true,
    confirm: "Save All",
    cancel: "Cancel",
    modal: false,
    state,
  },
);
```

### 2. Implementing Container Event Handlers

Add event handlers to control container behavior:

```javascript
customerManagementContainer.onSubmit = async () => {
  // Save all child documents
  for (let child of customerManagementContainer._children) {
    if (child.dataType === ALL_CONSTANTS.TYPES.DOC) {
      await child.save();
    }
  }
  "All customer data saved".sendResponse();
};

customerManagementContainer.onClose = async () => {
  // Clear any pending auto-save data
  customerManagementContainer.clearAutoSaveBuffer();
  customerManagementContainer.clearAutoSaveBufferChanges();
};
```

### 3. Adding Child Components

Add documents and collections to the container:

```javascript
// Add main customer document
customerManagementContainer.addChild(customerDocLib);

// Add related collections
customerManagementContainer.addChild(customersCollectionLib);
customerManagementContainer.addChild(customerContactsCollectionLib);

// Add sub-documents if needed
customerManagementContainer.addChild(customerAddressDocLib);
```

### 5. Implementing Advanced Actions

Add custom action buttons and handlers:

```javascript
// Configure additional action buttons
customerManagementContainer.more = [
  "Export Data",
  "Import Data",
  "Generate Report",
];

// Handle additional actions
customerManagementContainer.onMoreButtons = async () => {
  const action = state.messageFromUser.moreAction;

  switch (action) {
    case "Export Data":
      await exportCustomerData();
      break;
    case "Import Data":
      await showImportDialog();
      break;
    case "Generate Report":
      await generateCustomerReport();
      break;
  }
};
```

## Advanced Container Techniques

### Dynamic Container Content

Implement containers with dynamic content based on context:

```javascript
// Dynamic child management based on user role
customerManagementContainer.onResponse = async (self) => {
  const userRole = state.getField("currentUserRole");

  if (userRole === "admin") {
    // Add admin-specific components
    if (!self._children.find((c) => c.intentId === "adminPanel")) {
      self.addChild(adminPanelDocLib);
    }
  } else {
    // Remove admin components for regular users
    const adminPanel = self._children.find((c) => c.intentId === "adminPanel");
    if (adminPanel) {
      self.removeChild(adminPanel);
    }
  }
};
```

## Container Constants and Styles

The Container class supports various predefined constants and styles:

```javascript
CONTAINER_CONSTANTS.STYLE.CONTAINER; // Default container tabbed style
CONTAINER_CONSTANTS.STYLE.WIZARD; // Default container wizard style where the screens are presented after pressing next
CONTAINER_CONSTANTS.STYLE.BREADCRUMB; // Default container breadcrumb style where the children are added one by one and only the last one is focussed on the screen

// Usage in constructor
const styledContainer = new Container("styledContainer", {
  style: CONTAINER_CONSTANTS.STYLE.CONTAINER,
  // ... other options
});
```

## Conclusion

The frontm.ai Container class provides a powerful orchestration layer for building complex, multi-component user interfaces. By following the patterns outlined in this guide, developers can create sophisticated containers that effectively manage documents, collections, and other UI elements while maintaining consistency, performance, and user experience.

Containers serve as the backbone for complex business applications, enabling developers to create cohesive interfaces that handle multiple data sources and operations in a coordinated manner. The Container class's comprehensive feature set, from auto-save management to scoped content and dynamic behavior, makes it an essential tool for building enterprise-grade maritime applications with frontm.ai.
