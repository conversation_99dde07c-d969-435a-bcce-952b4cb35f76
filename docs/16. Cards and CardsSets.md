# Cards and CardsSets in frontm.ai

## Overview

The Card and CardsSet classes provide a flexible way to create card-based UI components in frontm.ai applications. Cards can display various types of content and respond to user interactions, making them ideal for dashboards, information displays, and interactive interfaces.

## Class Reference

### CardsSet Class

`CardsSet` is a container class that manages a collection of related cards.

#### Constructor

```javascript
import { CardsSet } from "@frontmltd/frontmjs/core/Card";
import { CARD_TYPES } from "@frontmltd/frontmjs/core/ALLConstants";
import { state } from "@frontmltd/frontmjs/core/State";

const cardSet = new CardsSet("myCardSet", {
  state, // Application state object
  type: CARD_TYPES.HTML, // Type of cards in this set (defaults to HTML)
  cardSetId, // Optional custom ID (defaults to a unique ID)
  readOnly: false, // Whether cards are read-only (defaults to false)
  tabId, // Optional tab ID for the card set
  prompt, // Optional prompt for AI assistance
  dynamic: false, // Whether cards are dynamically generated
  assistant, // Optional AI assistant configuration
  summaryOnResponse, // Optional summary to show on response
  summarisationPrompt, // Optional prompt for summarization
});
```

#### Key Properties

- `readOnly`: Controls whether cards in the set can be interacted with
- `cards`: Getter/setter for accessing the cards collection
- `cardSetId`: Unique identifier for the card set
- `prompt`: Text prompt for AI assistance with this card set

#### Key Methods

- `addCard(card)`: Adds a card to the set (must match the set's type)
- `clearCards()`: Removes all cards from the set
- `sendResponse()`: Renders the card set by updating application state
- `sendResponseWithDelay()`: Renders with a delay (useful for certain UI patterns)
- `message()`: Returns the message structure for the card set

### Card Class

`Card` represents an individual card with content and interactive capabilities.

#### Constructor

```javascript
import { Card } from "@frontmltd/frontmjs/core/Card";
import { CARD_TYPES } from "@frontmltd/frontmjs/core/ALLConstants";
import { state } from "@frontmltd/frontmjs/core/State";

const card = new Card("myCard", {
  state, // Application state object
  id, // Optional custom ID (defaults to intentId)
  content, // Content to display in the card
  contentReadOnlyState, // Optional read-only state for content
  type: CARD_TYPES.HTML, // Card type (defaults to HTML)
  cardsSet, // Parent CardsSet this card belongs to
  dynamic: false, // Whether card is dynamically generated
  prompt, // Optional prompt for AI assistance
  assistant, // Optional AI assistant configuration
  summaryOnResponse, // Optional summary to show on response
  summarisationPrompt, // Optional prompt for summarization
});
```

#### Key Properties

- `type`: The type of card (HTML, IMAGE, etc.)
- `onClick`: Setter/getter for the function to execute when clicked

#### Key Methods

- `sendResponse()`: Renders just this card by updating application state
- `message()`: Returns the message structure for the card
- `onMatching`: Returns a function that checks if a message matches this card
- `onResolution`: Returns a function that handles the card click event

## Card Types

Cards support different content types defined in `CARD_TYPES`:

- `HTML`: Standard HTML content
- `IMAGE`: Image display
- `MARKDOWN`: Markdown-formatted content
- `JSON`: JSON data display

## AI Integration

Both CardsSets and Cards now support AI integration through the following parameters:

- `prompt`: Text prompt for AI assistance
- `dynamic`: Whether content is dynamically generated
- `assistant`: Configuration for the AI assistant
- `summaryOnResponse`: Summary to display on response
- `summarisationPrompt`: Prompt for generating summaries

## Building Card Interfaces: Step-by-Step Guide

### 1. Create a Card Set

```javascript
import { CardsSet } from "@frontmltd/frontmjs/core/Card";
import { CARD_TYPES } from "@frontmltd/frontmjs/core/ALLConstants";
import { state } from "@frontmltd/frontmjs/core/State";

const dashboardCards = new CardsSet("dashboardCards", {
  state,
  type: CARD_TYPES.HTML,
  prompt: "Dashboard showing key metrics",
});
```

### 2. Create Individual Cards

```javascript
import { Card } from "@frontmltd/frontmjs/core/Card";

const summaryCard = new Card("summaryCard", {
  state,
  cardsSet: dashboardCards,
  content: "<div class='card-content'>Summary Information</div>",
  prompt: "Card showing summary information",
});

const statsCard = new Card("statsCard", {
  state,
  cardsSet: dashboardCards,
  content: "<div class='card-content'>Statistics</div>",
  prompt: "Card showing statistical information",
});
```

### 3. Define Click Handlers

```javascript
summaryCard.onClick = async () => {
  // Handle card click
  "Card clicked".sendResponse();
};
```

### 4. Display the Cards

```javascript
// Display all cards in the set
dashboardCards.sendResponse();

// Or display just a single card
summaryCard.sendResponse();
```

## Integration with Documents and Sections

Cards can be integrated with the Doc and Section classes:

```javascript
import { Doc } from "@frontmltd/frontmjs/core/Doc";
import { Section } from "@frontmltd/frontmjs/core/Section";
import { CardsSet } from "@frontmltd/frontmjs/core/Card";

// Create a document
const dashboardDoc = new Doc("dashboardDoc", state, {
  title: "Dashboard",
  autoSave: false,
});

// Create a section with a card set
const cardsSection = new Section("cardsSection", {
  title: "Information Cards",
  doc: dashboardDoc,
  state,
});

// Create and associate a card set with the section
const infoCards = new CardsSet("infoCards", {
  state,
  type: CARD_TYPES.HTML,
});

// Associate the card set with the section
cardsSection.cardsSet = infoCards;

// Now when you send the document response, it will include the cards
dashboardDoc.sendResponse();
```

## Example: Dashboard with Interactive Cards

```javascript
import { CardsSet, Card } from "@frontmltd/frontmjs/core/Card";
import { CARD_TYPES } from "@frontmltd/frontmjs/core/ALLConstants";
import { state } from "@frontmltd/frontmjs/core/State";
import { D } from "@frontmltd/frontmjs/core/State";

// Create a card set for the dashboard
const dashboardCards = new CardsSet("dashboardCards", {
  state,
  type: CARD_TYPES.HTML,
  prompt: "Dashboard showing business metrics",
});

// Create cards with different information
const salesCard = new Card("salesCard", {
  state,
  cardsSet: dashboardCards,
  content: "<div class='metric-card'><h3>Sales</h3><p>$1,234,567</p></div>",
  prompt: "Card showing sales metrics",
});

const ordersCard = new Card("ordersCard", {
  state,
  cardsSet: dashboardCards,
  content: "<div class='metric-card'><h3>Orders</h3><p>1,234</p></div>",
  prompt: "Card showing order metrics",
});

const customersCard = new Card("customersCard", {
  state,
  cardsSet: dashboardCards,
  content: "<div class='metric-card'><h3>Customers</h3><p>567</p></div>",
  prompt: "Card showing customer metrics",
});

// Add click handlers
salesCard.onClick = async () => {
  D.log({ message: "Sales card clicked" });
  // Show detailed sales information
};

ordersCard.onClick = async () => {
  D.log({ message: "Orders card clicked" });
  // Show detailed orders information
};

customersCard.onClick = async () => {
  D.log({ message: "Customers card clicked" });
  // Show detailed customer information
};

// Display the dashboard
dashboardCards.sendResponse();
```

## Advanced Example: AI-Assisted Cards

```javascript
import { CardsSet, Card } from "@frontmltd/frontmjs/core/Card";
import { CARD_TYPES } from "@frontmltd/frontmjs/core/ALLConstants";
import { state } from "@frontmltd/frontmjs/core/State";

// Create an AI-assisted card set
const aiDashboard = new CardsSet("aiDashboard", {
  state,
  type: CARD_TYPES.HTML,
  prompt: "Generate a dashboard with key metrics",
  dynamic: true,
  assistant: {
    model: "claude-3-sonnet-20240229",
    temperature: 0.7,
  },
  summaryOnResponse: "AI-generated dashboard",
});

// Create a dynamic card with AI assistance
const insightCard = new Card("insightCard", {
  state,
  cardsSet: aiDashboard,
  content: "<div class='ai-card'>Loading AI insights...</div>",
  prompt: "Generate insights based on recent data",
  dynamic: true,
});

// The card content can be dynamically updated based on AI responses
insightCard.onClick = async () => {
  // Handle AI-assisted interaction
  "Generating detailed insights...".sendResponse();
  // Additional AI processing logic here
};

// Display the AI-assisted dashboard
aiDashboard.sendResponse();
```

## Entry Method Support

The `sendResponseWithDelay` method supports different entry methods:

```javascript
import { ENTRY_METHOD } from "@frontmltd/frontmjs/core/ALLConstants";

// If using the NEXT entry method, the response will be delayed
// and sent through the context's tell method
if (state.entryMethod === ENTRY_METHOD.NEXT) {
  await dashboardCards.sendResponseWithDelay();
} else {
  // Otherwise, send immediately
  dashboardCards.sendResponse();
}
```

## Event Handling

Cards implement the Intent pattern for event handling:

```javascript
// The onMatching method determines if a user message matches this card
const isCardClicked = myCard.onMatching(state);

// The onResolution method handles the card click event
if (isCardClicked) {
  await myCard.onResolution(state);
}
```

By default, the `onMatching` method checks if the message type is a card response and if the card ID matches. The `onResolution` method calls the `onClick` handler and adds a silent response if needed.

## CardsSets and Forms

Forms can include CardsSets sections. For that developers need to only pass the CardsSet object to the section when creating the section as shown in the example below:

```javascript
import { CardsSet } from "@frontmltd/frontmjs/core/Card";
import { Section } from "@frontmltd/frontmjs/core/Section";
import { state } from "@frontmltd/frontmjs/core/State";

// Create a card set
const dashboardCards = new CardsSet("dashboardCards", {
  state,
  type: CARD_TYPES.HTML,
});

// Create a section with the card set
const cardsSection = new Section("cardsSection", {
  title: "Information Cards",
  doc: dashboardDoc,
  state,
  cardsSet: dashboardCards,
});
```

## Best Practices

1. **Card Organization**: Group related cards in the same CardsSet for logical organization
2. **Content Formatting**: Use CSS classes for consistent styling across cards
3. **Error Handling**: Always provide a cardsSet when creating a Card to avoid warnings
4. **Type Consistency**: Ensure all cards in a set have the same type
5. **Responsive Design**: Design card content to be responsive for different screen sizes
6. **AI Integration**: Use the AI-related parameters for dynamic content generation
7. **Event Handling**: Implement onClick handlers for interactive cards
8. **Message Matching**: Utilize the onMatching and onResolution methods for advanced interaction patterns
