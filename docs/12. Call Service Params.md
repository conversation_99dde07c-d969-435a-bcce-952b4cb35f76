# callService(serviceName, params)

## Description

`callService` is an asynchronous method that calls external RESTful APIs. Services must only be called from runOnCloud intents.

## Signature

```javascript
await state.api.callService(serviceName, params);
```

- **serviceName**: A string indicating the name of the service.
- **params**: An object containing the parameters needed by the service. This object can include query parameters, path parameters, header parameters, or a body. The structure is as follows:

```javascript
const params = {
  headers: {}, // Object for header parameters.
  queryParameters: "", // String added to the URL after path parameters.
  pathParameters: "", // String added to the URL.
  body: {}, // Used for PUT or POST services to include data in the body.
};
```
