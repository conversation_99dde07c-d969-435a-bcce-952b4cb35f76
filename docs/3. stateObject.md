# state Object in [frontm.ai](http://frontm.ai): A Deep Dive

The `state` object is a central component in the [frontm.ai](http://frontm.ai) framework, serving as the primary interface for managing application state, user interactions, and data flow. This guide provides an in-depth look at the `state` object, its methods, and its role in building interactive applications.

## Key public attributes of the State class based on the code:

### User & Session Related:

```text
user: Object                   // User information object
userName: string               // User's name
userEmail: string             // User's email
userId: string                // User's ID
botId: string                 // Bot identifier
botName: string               // Bot name
botDescription: string        // Bot description
lastSession: number           // Last session timestamp
amIOnline: boolean            // Online status
```

### System & Configuration:

```text
persistentState: boolean       // (readonly) State persistence flag
persistentOfflineData: boolean // Offline data persistence flag
systemId: string               // (readonly) System identifier
conversational: boolean        // Conversational mode flag
```

### UI Related:

```text
background: {                 // Background configuration
  type: number
}
sidebar: {                    // Sidebar configuration
  hidden: boolean,
  frozen: boolean
}
navbar: {                     // Navbar configuration
  hidden: boolean,
  frozen: boolean
}
waitingIcon: Object           // Waiting icon configuration
suggestionsLayout: string     // Suggestions layout ("horizontal" | "vertical")
chatButtonHidden: boolean     // Chat button visibility
cssFilePath: string | null    // CSS file path
modal: boolean               // Modal state
```

### Utility Objects:

```text
notification: Notification        // Instance of Notification class
accounts: Accounts               // Instance of Accounts class
orders: Orders                   // Instance of Orders class
sftp: SFTP | {}                 // Instance of SFTP class or empty object
marketplace: Marketplace         // Instance of Marketplace class
jobScheduler: JobScheduler      // Instance of JobScheduler class
contacts: Contacts              // Instance of Contacts class
api: API                        // Instance of API class
db: DB                          // Instance of DB class
file: File | {}                 // Instance of File class
sites: Sites                    // Instance of Sites class
sensors: Sensors                // Instance of Sensors class
locationServices: LocationServices // Instance of LocationServices class
security: Security              // Instance of Security class
nlp: NLP                        // Instance of NLP class
cc: ContactCentre              // Instance of ContactCentre class
companies: Companies            // Instance of Companies class
schedules: Schedules           // Instance of Schedules class
deviceStorage: DeviceStorage   // Instance of DeviceStorage class
```

### Core Properties

```text
_: Object                  // (readonly) Lodash utility library instance (getter: _)
moment: Object                 // Moment.js library instance
momentTimezone: Object         // Moment timezone capability object
```

### Error Handling

```text
error: boolean               // Error state
errorStack: Array<Error>     // Array of Error instances
initializationErrorMessages: Array<string> // Array of initialization error messages
```

### Additional properties

```text
client: string               // Client type identifier (WEB, MOBILE, API)
conversationId: string       // Conversation unique identifier
lang: string                 // Language setting (defaults to "en")
location: Object             // Location information (CLOUD, EDGE)
context: Object | null       // Context object
contextAware: boolean         // Context-aware state
currentAction: string        // Current action identifier
currentMenuSelection: string // Current menu selection identifier
currentUserDomain: string    // Current user domain
currentTabId: string        // Current tab identifier
ipDetails: Object           // IP-related details
messageFromUser: string      // Current message from user
messageTypeFromUser: string  // Type of message from user
platform: string            // Platform identifier
osVersion: string          // Operating system version
brand: string              // Device brand
phoneModel: string         // Device model
userAgent: string          // User agent string
activeIntent: string         // (readonly) Active intent identifier
```

## User Object

```text
state.user: {
    info: {                    // Optional object containing user information
        userName: string,      // Username from system
        userId: string,        // User's ID from system
        domains: string[],     // Available domains for the user
        lastLoggedInDomain: string,  // Last domain user logged into
    },
    userName: string,          // Username (synced from info.userName or directly set)
    userId: string,            // User ID (synced from info.userId)
    userEmail: string,         // User's email address
    tz: string,               // User's timezone
    userDomains: string[],     // Available domains (synced from info.domains)
    roles: string[],          // User roles for current domain
}
```

Example usage:

```javascript
// Examples of accessing user properties
const username = state.user.userName;
const timezone = state.user.tz;
const userRoles = state.user.roles;
const domains = state.user.userDomains;
const userInfo = state.user.info;
```

## Key public methods of the State class based on the code:

### Field Management Methods

#### Fields

- `setSharedField(fieldName, fieldValue, expire)`: Sets a field shared across sessions
- `getSharedField(fieldName)`: Retrieves a shared field
- `clearSharedField(fieldName)`: Clears a shared field
- `setField(fieldName, fieldValue)`: Sets a field value
- `getField(fieldName)`: Gets a field value
- `clearField(fieldName)`: Clears a field value
- `setPersistedField(fieldName, fieldValue)`: Sets a persisted field

### Static Data Methods

### Data Retrieval

- `getMultipleStaticDataKeys(keys, system)`: Retrieves multiple static data entries
- `getStaticData(key, system, shared = false, encryptedValue = false)`: Gets single static data entry
- `getEncryptedStaticData(key, system, shared = false)`: Gets encrypted static data
- `getSystemStaticData(key, shared = false)`: Gets system-level static data
- `saveStaticData(key, value)`: Saves static data

### System Methods

#### Network and Authentication

- `online()`: Checks online status
- `logout(appType)`: Handles logout process
- `getUserRolesForDomain(domain)`: Gets user roles for a specific domain
- `getSignedUrl(bucket, key)`: Generates a signed URL

### Error Handling

### Error Management

- `resetOnErrorMethod()`: Resets error handling to default
- `defaultOnError()`: Default error handling implementation
- `addSystemErrorToStack(errorCode, errorMessage)`: Adds an error to the error stack
- `clearError()`: Clears all errors from the error stack

### Request Operations

- `getUniqueId()`: Generates a unique identifier

### Response Management

#### Basic Response Methods

- `addResponse(type, message)`: Base method for adding responses
- `addChatResponse(message)`: Adds a chat type response
- `addAiccResponse(message)`: Adds an AICC string type response

#### Notification Responses

- `addStandardNotificationResponse(message)`: Adds a standard notification
- `addCriticalNotificationResponse(message)`: Adds a critical notification
- `addAuthorizationRequestResponse(message)`: Adds an authorization request

#### Special Responses

- `addSilentResponse()`: Adds a silent response

#### Response Management

- `doWeHaveResponse()`: Checks if there are any non-silent responses

### NLP Operations

- `clearThreadId()`: Clears the current thread ID

### Flow Operations

- `continueInIntentWithIdAndMessage(intentId, message)`: Continues in a specific intent with a message after the current intent ends

### Smart Suggestions Management

- `addSmartSuggestions(newSmartSuggestion)`: Adds a single smart suggestion
- `addSmartSuggestionsArray(newSmartSuggestions)`: Adds multiple smart suggestions filtering by language
- `addEnglishSmartSuggestions(newSmartSuggestions)`: Adds English-specific smart suggestions
- `clearSmartSuggestionsArray()`: Clears all smart suggestions and resets block flag
