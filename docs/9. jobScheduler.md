# Job Scheduler in [frontm.ai](http://frontm.ai): A Comprehensive Guide

The Job Scheduler is a powerful feature within the [frontm.ai](http://frontm.ai) framework, designed to automate and schedule tasks within your application. This guide provides an in-depth look at how to use the Job Scheduler effectively, including its core concepts, configuration, and best practices.

## Overview of the Job Scheduler

This framework is a message based framework which means that when you schedule a process what you are doing is to schedule a message that will be sent to a micro-app which will resolve it using an intent.

This is a simple example on how the scheduler is used:

```javascript
import { state } from "@frontmltd/frontmjs/core/State";

await state.jobScheduler.scheduleMessage({
  toUser: "userId123",
  messages: {
    intentId: "someIntent",
    data: "someData",
  },
  schedule: Date.now() + 60 * 1000, // Schedule 1 minute from now
});
```

Key parameters:

1. toBot: Target bot ID (defaults to current bot)
2. toUser: Single user ID or array of user IDs
3. messages: Single message or array of messages to send
4. jobId: Unique ID for the job (auto-generated if not provided)
5. schedule: Timestamp (epoch) when message should be sent (defaults to Date.now())
6. repeats: Optional scheduling pattern for recurring messages
   - quantity: Number of times to repeat (must be > 0)
   - unit: Time unit for repetition as defined within ALL_CONSTANTS

```javascript
export const SCHEDULE_INTERVALS = {
  SECOND: "second",
  MINUTE: "minute",
  HOUR: "hour",
  DAY: "day",
  WEEK: "week",
  MONTH: "month",
};
```

```javascript
import { state } from "@frontmltd/frontmjs/core/State";
import { SCHEDULE_INTERVALS } from "@frontmltd/frontmjs/core/ALLConstants";

await state.jobScheduler.scheduleMessage({
  toBot: "targetBot",
  toUser: ["user1", "user2"], // Multiple users
  messages: [
    // Multiple messages
    { type: "text", content: "Message 1" },
    { type: "text", content: "Message 2" },
  ],
  jobId: "custom-job-123",
  schedule: Date.now() + 86400000, // 24 hours from now
  repeats: {
    quantity: 5, // Must be > 0
    unit: SCHEDULE_INTERVALS.DAY, // Time unit
  },
});
```
