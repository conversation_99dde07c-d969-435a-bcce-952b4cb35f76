# Connecting an LLM to frontm.ai

frontm.ai micro-apps can use LLMs to determine the intention of a message, handle the intent matching process, or even resolve the intent fully.

## How to connect an LLM to frontm.ai

There are multiple ways to integrate an LLM into frontm.ai:

1. **Let the framework handle it**: All functionalities are enabled automatically. No special coding is required in the micro-app.
2. **Use a lambda to connect to the LLM**: Sometimes there's a need to use a very specific LLM training that integrates other services. In these situations, having a specialized function can help. This can be done through a lambda that calls the external services.
3. **Use the service architecture to call LLM APIs**: This approach calls openAI or other LLM services directly. This is less common and possibly not required since options 1 or 2 should cover most needs.

### Integrating an LLM to the framework

The standard architecture to integrate LLMs has the following components:

1. An LLM Agent in AWS Bedrock or an LLM Assistant in OpenAI
2. An AWS Secret Manager LLM configuration with API keys and agent identifiers
3. A micro-app that uses the LLM to resolve the intent

#### Default framework integration

When a micro-app is deployed, the deployment uses a systemId. The framework will always try to use an assistant identified with this systemId as the default.

If you need a different default assistant, you can specify it in the state object using the onConfig event:

```javascript
state.onConfig = async () => {
  state.nlp.assistantName = "myCustomAssistantName";
};
```

When a message arrives at the micro-app and no rule directly matches the content, the framework will forward the message to the default assistant to perform matching and attempt to provide a resolution.

If the Secret Manager configuration is incorrect, the micro-app will respond with an error 500.

#### Secret Manager Config

The Secret Manager must be configured with a JSON object using the following structure:

AWS Bedrock:

```json
{
  "provider": "bedrock",
  "agentId": "123456789",
  "agentAliasId": "123456789"
}
```

OpenAI:

```json
{
  "provider": "openai",
  "apiKey": "123456789",
  "assistantId": "123456789"
}
```

This configuration can be added by the DevOps team.

#### LLM responses

When training an LLM to communicate directly with frontm.ai, there are specific rules to follow for the response format. The LLMs must always return a JSON object with the following structure:

```json
{
  "response": "This is a text response to the user",
  "suggestions": [
    "These are suggestions displayed to the user in the UI as next questions to ask",
    "Suggestion 2",
    "Etc"
  ],
  "intentId": "When using LLM as matching mechanism, the LLM must return the intentId to match",
  "other arbitrary fields": "These fields are added to the message metadata and can be used in the micro-app"
}
```

If the response doesn't follow this format, the framework won't be able to process it and will return error 500.

If the LLM is used for matching only, the logic to create the response needs to be implemented in the micro-app by adding a matching rule like this:

```javascript
myIntent.onMatching = () => {
  return state.nlp.lastNLPResponse.intentId === MY_INTENT_ID;
};
myIntent.onResolution = async () => {
  "This is the response".sendResponse();
};
```

The state stores the LLM response in the `lastNLPResponse` object.

#### Responses

The most common responses are string responses. frontm.ai extends the JavaScript String primitive with two methods:

- `sendResponse()`: Sends a response to the user that is stored in the conversation history
- `sendEphemeralResponse()`: Sends a response to the user that is not stored in the conversation history

The intent can also send other types of responses such as tables or forms. This way, the micro-app can create a form from the LLM response and send it to the user.

#### Calling an assistant manually

The micro-app can call the assistant manually using the `callAgentWithMessage` method in the state. For example:

```javascript
myIntent.onResolution = async () => {
  const nlpResult = await state.nlp.callAgentWithMessage({
    userPrompt: "Hello world!",
  });
  nlpResult.response?.sendResponse();
};
```

### Using specialized lambda to connect to LLMs

The framework cannot use specialized lambdas to perform matching. In these cases, implement this by using a rule to catch all messages and then send them to the lambda for processing. For example:

```javascript
myIntent.onMatching = () => {
  return true;
};
myIntent.onResolution = async () => {
  const response = await frontmlib.invokeLambda(
    "myLambdaName",
    frontmlib.LAMBDA_INVOCATION_TYPES.REQUEST_RESPONSE,
    { message: state.messageFromUser.content },
  );
  response.sendResponse();
};
```

If your requirement is to pass a text message from the user to a lambda that processes it using an LLM, you can catch all messages and send them to the lambda as shown in the example above.

#### Using the api object to call an LLM RESTful API

Similar to using a lambda, you can call APIs directly from the micro-app:

```javascript
myIntent.onMatching = () => {
  return true;
};
myIntent.onResolution = async () => {
  const response = await state.api.callService("myServiceName", {
    body: { payload: "Hello world!" },
  });
  response.sendResponse();
};
```
