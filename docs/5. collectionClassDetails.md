# The Collection Class in [frontm.ai](http://frontm.ai): Comprehensive Event Documentation

The Collection class is a fundamental component of the [frontm.ai](http://frontm.ai) framework's data modelling system, extending the powerful Intent class architecture. While the basic documentation covers its structure and initialisation, this expanded guide focuses specifically on understanding and utilising the Collection class's extensive event system and related functionality.

## Overview of the Collection Class

The Collection class manages groups of documents, implementing table-like structures that can display, filter, and interact with multiple document instances. It forms a critical part of [frontm.ai](http://frontm.ai)'s unified architecture, where data presentation and interaction are seamlessly integrated.

```javascript
import { Collection } from "@frontmltd/frontmjs/core/Collection";
import { D, state } from "@frontmltd/frontmjs/core/State";

export let customersCollectionLib = new Collection("customersCollectionLib", {
  title: "Customers",
  document: customerDocLib,
  allowClose: true,
  cache: false,
  shared: true,
  name: "customers",
  allowQuickActions: true,
  state,
});
customersCollectionLib.runOnCloud();
```

## Collection Constructor Parameters In Detail

The Collection constructor accepts a rich set of parameters that control its behavior, appearance, and functionality:

```javascript
const collection = new Collection(intentId, {
  // Basic display properties
  title: "Customers",            // Display title for the collection
  description: "Customer list",  // Description text
  document: customerDocLib,      // Associated document class
  name: "customers",             // Collection name in database

  // UI control options
  promptOnClose: true,           // Confirm before closing
  closeOnAction: true,           // The collection closes on submit or cancel

  // Table display options
  emptyStateMessage: "No data",  // Message when empty
  searchPlaceholder: "Search",   // Placeholder for search

  // Row interaction options
  selectableRows: false,         // Allow row selection
  allowEdit: true,               // Allow row editing
  allowDelete: true,             // Allow row deletion
  allowDeleteWhileOffline: false,// Allow deletion while offline

  // Advanced functionality
  addNewRows: true,              // Enable adding new rows
  addNewRowsLabel: "Add",        // Label for add button
  allowQuickAction: true,        // Enable quick actions
  allowSearch: true,             // Enable search functionality
  allowRefresh: true,            // Enable refresh button
  allowDownload: true,           // Enable download option

  // Data handling
  shared: true,                  // Make collection shareable with a domain
  cache: true,                   // Enable caching
  local: false,                  // Use local storage
  updateInBackground: false,     // Update without UI refresh

  // Visual customization
  style: "table",                // Table, calendar, map, etc.
  modal: false,                  // Display as modal
  topTableMenu: false,           // Show menu at top

  //MAP Options
  mapOptions: {}                 // See the maps documentation for details

  //AI
  prompt: "Reason for this collection to exist",

  // Collection state
  state: state,                  // Application state
});
```

## Collection Events System

The Collection class provides a comprehensive event system that enables developers to customise collection behaviour at different interaction points. These events are automatically triggered by user actions and internal processes.

### Core Navigation Events

```javascript
// Called when a collection is closed
customersCollectionLib.onClose = async () => {
  // Perform cleanup operations
  "Collection closed.".sendEphemeralResponse();
};

// Called when previous/next page buttons are clicked
customersCollectionLib.onPreviousPage = async () => {
  // Load the previous page of data
  await loadPreviousCustomersPage();
};

customersCollectionLib.onNextPage = async () => {
  // Load the next page of data
  await loadNextCustomersPage();
};
```

### Row Modification Events

```javascript
// Called when a row is saved during in line edition
customersCollectionLib.onSave = async () => {
  "In-line save clicked".sendEphemeralResponse();
};

// Called when a row is deleted
customersCollectionLib.onDelete = async () => {
  "In-line delete clicked".sendEphemeralResponse();
};

// Called before a row is deleted (confirmation)
customersCollectionLib.onPreDelete = async () => {
  // Can be used to show confirmation dialog
  customersCollectionLib.sendDeleteConfirmDialogResponse({
    title: "Confirm Deletion",
    message: "Are you sure you want to delete this customer?",
    confirm: "Delete",
  });
};

// Called when changes are discarded
customersCollectionLib.onDiscard = async () => {
  // Handle discarded changes
  "Changes discarded.".sendResponse();
};
```

### Selection and Menu Events

```javascript
// Called when a row is selected (checkbox)
customersCollectionLib.onSelection = async () => {
  // Handle row selection
  const selectedDocId = state.messageFromUser.docId;
  // Process the selection
};

// Called when multiple rows are selected
customersCollectionLib.onMultipleSelection = async () => {
  // Handle multiple selection
  const selectedDocIds = state.messageFromUser.selectedDocIds;
  // Process multiple selections
};

// Called when a menu action is triggered
customersCollectionLib.onMenuAction = async () => {
  // state is imported
  const action = state.messageFromUser.menuAction;
  const docId = state.messageFromUser.docId;

  // Handle different menu actions
  if (action === "archive") {
    // Archive the customer
  } else if (action === "export") {
    // Export customer data
  }
};

// Called when a field-specific action is triggered
customersCollectionLib.onFieldAction = async () => {
  // state is imported
  const fieldId = state.messageFromUser.fieldId;
  const docId = state.messageFromUser.docId;
  // Handle field-specific actions
};

// Called when a top table menu action is triggered
customersCollectionLib.onTopMenuAction = async () => {
  // state is imported
  const action = state.messageFromUser.menuAction;
  // Handle top menu actions
};

// Called for quick actions (icons in rows)
customersCollectionLib.onQuickAction = async () => {
  // state is imported
  const action = state.messageFromUser.quickAction;
  const docId = state.messageFromUser.docId;
  // Handle quick actions
};
```

### Data Management Events

```javascript
// Called when collection refresh is requested
customersCollectionLib.onRefresh = async () => {
  // Reload data from source
  customersCollectionLib.clearRows();
  await getCustomersList();
  customersCollectionLib.sendResponse();
};

// Called when search is performed
customersCollectionLib.onSearch = async () => {
  // state is imported
  const searchString = state.messageFromUser.searchString;
  // Perform search operation
  const query = customersCollectionLib.getQueryForSearch(searchString);
  customersCollectionLib.clearRows();
  await customersCollectionLib.loadCollectionWithQuery({
    query: query,
    limit: 20,
  });
  customersCollectionLib.sendResponse();
};

// Called when search is cleared
customersCollectionLib.onClearSearch = async () => {
  // Clear search and reset
  customersCollectionLib.activeQueryString = null;
  customersCollectionLib.clearRows();
  await getCustomersList();
  customersCollectionLib.sendResponse();
};

// Called when download is requested
customersCollectionLib.onDownload = async () => {
  // Generate download file
  "Download started.".sendResponse();
};
```

### Filter Management Events

```javascript
// Called when a filter is applied
customersCollectionLib.onFilter = async () => {
  // state is imported
  const filterName = state.messageFromUser.filterName;
  const filterCriteria =
    customersCollectionLib.getFilterCriteriaWithName(filterName);

  // Apply the filter
  customersCollectionLib.activeFilterName = filterName;
  customersCollectionLib.clearRows();
  await customersCollectionLib.loadCollectionWithQuery({
    query: filterCriteria,
    limit: 20,
  });
  customersCollectionLib.sendResponse();
};

// Called when a filter is deleted
customersCollectionLib.onFilterDelete = async () => {
  // state is imported
  const filterName = state.messageFromUser.filterName;
  // Delete the filter from storage
};

// Called when a filter is edited
customersCollectionLib.onFilterEdit = async () => {
  // state is imported
  const filterName = state.messageFromUser.filterName;
  // Edit the filter
  customersCollectionLib.sendEditFilterResponse();
};

// Called when a filter is renamed
customersCollectionLib.onRenameFilter = async () => {
  // state is imported
  const oldName = state.messageFromUser.oldName;
  const newName = state.messageFromUser.newName;
  // Rename the filter
};

// Called when filters are cleared
customersCollectionLib.onClearFilter = async () => {
  // Clear active filter
  customersCollectionLib.activeFilterName = null;
  customersCollectionLib.clearRows();
  await getCustomersList();
  customersCollectionLib.sendResponse();
};
```

### Map and Calendar View Events

```javascript
// Called when map overlay is created (for map collections)
customersCollectionLib.onCreateOverlay = async () => {
  // Handle overlay creation
};

// Called when map overlay is edited
customersCollectionLib.onEditOverlay = async () => {
  // Handle overlay editing
};

// Called when map overlay is deleted
customersCollectionLib.onDeleteOverlay = async () => {
  // Handle overlay deletion
};

// Called when areas are shown (for map collections)
customersCollectionLib.onShowAreas = async () => {
  // Handle showing areas
};
```

## Key Collection Methods for Data Operations

### Loading and Querying Data

```javascript
// Load collection with MongoDB-style query
await customersCollectionLib.loadCollectionWithQuery({
  query: { status: "ACTIVE" }, // MongoDB query object
  projection: { _id: 0 }, // Fields to include/exclude
  sort: { customerName: 1 }, // Sort order
  skip: 0, // Number of records to skip
  limit: 20, // Max records to return
  cache: true, // Use cache or not
});

// Count documents matching a query
const count = await customersCollectionLib.countCollectionWithQuery({
  query: { status: "ACTIVE" },
});
```

### Managing Collection Rows

```javascript
// Clear all rows
customersCollectionLib.clearRows();

// Add a row (document instance)
const newCustomer = customerDocLib.cloneDoc();
customersCollectionLib.addRow(newCustomer);

// Build rows from array of data objects
const customersData = [
  { customerId: "CUST-001", customerName: "Acme Corp" },
  { customerId: "CUST-002", customerName: "Globex Inc" },
];
customersCollectionLib.buildRowsFromArray(customersData);

// Insert row at specific position
customersCollectionLib.insertRow(0, newCustomer);

// Get all rows as documents array
const documentsArray = customersCollectionLib.rowAsDocumentsArray();
```

### UI Responses and Display

```javascript
// Send collection as response (full update)
customersCollectionLib.sendResponse();

// Send short response (optimized format)
customersCollectionLib.sendShortResponse(false, 20);

// Send response to clear rows
customersCollectionLib.sendClearRowsResponse();

// Send delete confirmation dialog
customersCollectionLib.sendDeleteConfirmDialogResponse({
  title: "Confirm Delete",
  message: "Are you sure you want to delete this customer?",
  confirm: "Delete",
});

// Send filter editing interface
customersCollectionLib.sendEditFilterResponse();

// Send filter change response
customersCollectionLib.sendFilterChangeResponse("FILTER_ACTION", [
  /* filtered columns */
]);
```

### Auto-save Management

```javascript
// Generate auto-save buffer from collection
customersCollectionLib.generateAutoSaveBufferFromCollection();

// Clear auto-save buffer
customersCollectionLib.clearAutoSaveBuffer();

// Clear auto-save buffer changes
customersCollectionLib.clearAutoSaveBufferChanges();

// Load rows from auto-save buffer
customersCollectionLib.loadRowsFromAutoSaveBuffer();
```

## Working with Filters

The Collection class provides powerful filtering capabilities that allow users to save and reuse query criteria:

```javascript
// Setting up a filter document
import { Doc } from "@frontmltd/frontmjs/core/Doc";
import { Field } from "@frontmltd/frontmjs/core/Field";
import { FormFieldTypes } from "@frontmltd/frontmjs/core/FormFieldTypes";

const customerFilterDoc = new Doc("customerFilterDoc", state, {
  title: "Customer Filter",
  autoSave: true,
});

const statusField = new Field("statusField", {
  title: "Status",
  doc: customerFilterDoc,
  type: FormFieldTypes.DROPDOWN,
  options: ["ACTIVE", "INACTIVE", "ALL"],
  state,
});

// Associate filter document with collection
customersCollectionLib.filterDoc = customerFilterDoc;

// Getting filter criteria by name
const activeCriteria =
  customersCollectionLib.getFilterCriteriaWithName("activeCustomers");
```

## Example: Complete Customer Collection Implementation

```javascript
import { Collection } from "@frontmltd/frontmjs/core/Collection";
import { Doc } from "@frontmltd/frontmjs/core/Doc";
import { D, state } from "@frontmltd/frontmjs/core/State";

// Create customer document
const customerDocLib = new Doc("customerDocLib", state, {
  title: "Customer",
  autoSave: true,
});

// Create customer collection
const customersCollectionLib = new Collection("customersCollectionLib", {
  title: "Customers",
  document: customerDocLib,
  allowClose: true,
  allowEdit: true,
  allowDelete: true,
  allowSearch: true,
  addNewRows: true,
  shared: true,
  name: "customers",
  state,
});
customersCollectionLib.runOnCloud();

// Handle collection refresh
customersCollectionLib.onRefresh = async () => {
  await loadCustomers();
};

// Load customers from database
const loadCustomers = async () => {
  customersCollectionLib.clearRows();
  await customersCollectionLib.loadCollectionWithQuery({
    query: {},
    limit: 100,
  });
  customersCollectionLib.sendResponse();
};

// Handle row click
customersCollectionLib.onAction = async () => {
  const state = customersCollectionLib._state;
  const customerId = state.messageFromUser.docId;

  await customerDocLib.loadDocument({ customerId });
  customerDocLib.sendResponse();
};

// Handle search
customersCollectionLib.onSearch = async () => {
  const state = customersCollectionLib._state;
  const searchText = state.messageFromUser.searchString;

  customersCollectionLib.activeQueryString = searchText;
  customersCollectionLib.clearRows();

  const query = customersCollectionLib.getQueryForSearch(searchText);
  await customersCollectionLib.loadCollectionWithQuery({
    query,
    limit: 20,
  });

  customersCollectionLib.sendResponse();
};

// Handle delete
customersCollectionLib.onPreDelete = async () => {
  customersCollectionLib.sendDeleteConfirmDialogResponse({
    title: "Delete Customer",
    message:
      "Are you sure you want to delete this customer? This action cannot be undone.",
    confirm: "Delete",
  });
};

customersCollectionLib.onDelete = async () => {
  const state = customersCollectionLib._state;
  const customerId = state.messageFromUser.docId;

  await customerDocLib.loadDocument({ customerId });
  await customerDocLib.delete();

  "Customer deleted successfully.".sendResponse();

  customersCollectionLib.clearRows();
  await loadCustomers();
};
```

## Conclusion

The Collection class in [frontm.ai](http://frontm.ai) provides a comprehensive system for managing and displaying collections of documents with rich interactive capabilities. Its extensive event system allows developers to customize behavior at various interaction points, from loading and filtering data to handling user actions and UI responses.

By leveraging these events effectively, developers can create sophisticated collection-based interfaces that respond intelligently to user actions while maintaining clean, maintainable code. The Collection class seamlessly integrates with the rest of the [frontm.ai](http://frontm.ai) architecture, particularly the Doc and Field classes, to create a cohesive data management system.

Understanding the Collection events system is key to building effective [frontm.ai](http://frontm.ai) applications that provide intuitive and responsive user experiences for maritime business applications.
