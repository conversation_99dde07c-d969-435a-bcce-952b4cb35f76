# Menu System Developer Documentation

## Overview

The Menu System provides a flexible way to create hierarchical menu structures with custom styling and behaviour. Built around two main classes (`Menu` and `MenuEntry`), it allows developers to construct everything from simple lists to complex nested menu trees.

## Class Reference

### Intent (Base Class)

Both `Menu` and `MenuEntry` extend from the `Intent` class, which provides base functionality for handling user interactions and maintaining state.

### Menu Class

`Menu` is the container class that represents a top-level menu.

#### Constructor

```javascript
import { Menu } from "@frontmltd/frontmjs/core/Menu";
const menu = new Menu(intentId, {
  state, // Application state object
  title, // Display title (defaults to "Menu")
  pullDown, // Boolean flag for dropdown behavior (defaults to false, card menus)
  columns, // Number of columns (defaults to 3)
});
```

#### Key Methods

- `sendResponse()`: Renders the menu by updating application state

### MenuEntry Class

`MenuEntry` represents individual menu items or submenu containers.

#### Constructor

```javascript
import { MenuEntry } from "@frontmltd/frontmjs/core/MenuEntry";

const myMenuEntry = new MenuEntry(intentId, {
  state, // Application state object
  menu, // Parent Menu object (required if menuEntry not provided)
  myParentmenuEntry, // Parent MenuEntry object (required if menu not provided)
  label, // Display text
  icon, // Icon identifier
  description, // Additional descriptive text
  descriptionColor, // Color for description text
  color, // Background color
  titleColor, // Color for the label text
  hidden, // Boolean flag to hide entry (defaults to false)
  order, // Display order (defaults to 999)
  dynamic, // Indictes if this menu was generated dynamically by code
  prompt, // Associated prompt for LLMs
});
```

#### Key Methods

- `onClick`: Setter/getter for the function to execute when clicked

## Building Menus: Step-by-Step Guide

### 1\. Create a Main Menu

```javascript
import { Menu } from "@frontmltd/frontmjs/core/Menu";
const mainMenu = new Menu("mainMenu", {
  state,
  title: "Application Menu",
  columns: 3,
});
```

### 2\. Add Top-Level Menu Sections

```javascript
import { MenuEntry } from "@frontmltd/frontmjs/core/MenuEntry";
const fileMenu = new MenuEntry("fileMenu", {
  state,
  menu: mainMenu, // Link to parent menu
  label: "File",
  icon: "file-icon",
  color: "#4A86E8",
  order: 1, // Lower numbers appear first
});
```

### 4\. Add Menu Items to Sections

```javascript
const newFileEntry = new MenuEntry("newFile", {
  state,
  menuEntry: fileMenu, // Link to parent section
  label: "New File",
  icon: "new-file-icon",
  description: "Create a new file",
  order: 1,
});
```

### 5\. Define Click Handlers

```dart
newFileEntry.onClick = async () => {
  D.log({message: "Menu clicked"});
  // Implementation of click function
};
```

When generating code for leaves menuEntries, always add a basic `onClick` method.

6\. Create Nested Submenus

```javascript
// First, create a submenu container
const advancedMenu = new MenuEntry("advancedMenu", {
  state,
  menuEntry: fileMenu, // This makes it a submenu of File
  label: "Advanced",
  icon: "advanced-icon",
  order: 3,
});

// Then add items to the submenu
const specialFileEntry = new MenuEntry("specialFile", {
  state,
  menuEntry: advancedMenu, // Link to submenu
  label: "Special File",
  icon: "special-file-icon",
  order: 1,
});

specialFileEntry.onClick = async () => {
  // Handler implementation
};
```

### 7\. Render the Menu

```javascript
import { Intent } from "@frontmltd/frontmjs/core/Intent";
import { D, state } from "@frontmltd/frontmjs/core/State";
import { SYSTEM_INTENTS } from "@frontmltd/front/core/ALLConstants";

export let main = Intent.create({
  intentId: SYSTEM_INTENTS.MAIN,
  prompt: "This is another example of the main intent",
  state,
});
main.runOnCloud();
main.onResolution = async () => {
  mainMenu.sendResponse();
};
```
