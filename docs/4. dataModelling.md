# Data Modelling in [frontm.ai](http://frontm.ai): A Comprehensive Guide

The [frontm.ai](http://frontm.ai) framework provides robust data modelling capabilities for building effective maritime business applications. This document expands on the existing documentation, particularly focusing on the Doc class which serves as the foundation for creating structured data models that automatically implement UI components and database operations.

## Overview of Data Modelling

[frontm.ai](http://frontm.ai) employs a unified architecture where all elements are represented by the `Intent` class and its derivatives. Data modelling leverages specialised extensions of this class to create a complete solution that automatically implements UI components and database operations.

The data model implementation shown in this guide follows best practices for creating scalable, maintainable applications that align with the [frontm.ai](http://frontm.ai) architectural principles.

## Core Classes for Data Modelling

### Document Class: In-Depth Examination

The `Doc` class is a foundational component in [frontm.ai](http://frontm.ai)'s data modelling system. It represents individual records or entities and forms the basis for data entry forms and database operations. The `Doc` class extends the `Intent` class, inheriting its messaging and resolution capabilities while adding specialised features for data handling.

#### Doc Class Constructor in Detail

The `Doc` constructor accepts a variety of parameters that control its behaviour and appearance:

```javascript
import { Doc } from "@frontmltd/frontmjs/core/Doc";
import { D, state } from "@frontmltd/frontmjs/core/State";

export let customerDocLib = new Doc("customerDocLib", state, {
  // Basic display properties
  title: "Customer", // Display title for the form
  description: "Customer form", // Description of the document
  icon: "customer-icon", // Icon to display

  // Form control buttons
  confirm: "Save", // Label for submit button
  cancel: "Cancel", // Label for cancel button

  // Behaviour controls
  closeOnAction: true, // Close form after confirmation
  updateInBackground: false, // Update in background without showing
  promptOnClose: true, // Prompt before closing
  readOnly: false, // Set form as read-only
  allowEdit: true, // Allow editing while display on row

  // Database-related properties
  autoSave: true, // Enable automatic saving
  audit: true, // Enable audit trail
  docId: "123456", // Document ID
  multiDomain: false, // Support crossed-domain operations

  // UI customization
  modal: false, // Display as modal dialog

  //AI
  prompt: "Reason for this Doc to exists",

  // Advanced features
  allowDelete: true, // Allow deletion while display on row
  allowDeleteWhileOffline: true, // Allow deletion while offline
  temp: false, // Temporary document (not persisted)
});
customerDocLib.runOnCloud(); // Run the document logic on the cloud
```

#### Document Event Handlers

The `Doc` class provides several event handlers that allow you to customise its behaviour:

```javascript
// Called when the document is submitted from UI
customerDocLib.onSubmit = async (self) => {
  await self.save();
  // Additional post-save logic
};

// Called before the document is saved to database
customerDocLib.onSave = async (self) => {
  // Perform validation or transformations before save
  if (!self.f.customerName.value) {
    throw new Error("Customer name is required");
  }
};

// Called after document is loaded from database
customerDocLib.onPostLoad = async (self) => {
  // Process data after loading
  if (self.f.status.value === "INACTIVE") {
    self.f.notes.readOnly = true;
  }
};

// Called when the document form is canceled in UI
customerDocLib.onCancel = async (self) => {
  // Handle cancellation logic
};

// Called when the document form is closed in UI
customerDocLib.onClose = async (self) => {
  // Perform cleanup operations
};

// Called after building the document from data
customerDocLib.onPostBuild = async (self, doc) => {
  // Custom logic after document is constructed
};

// Called after building the container (for sub-docs)
customerDocLib.onPostBuildContainer = async (self, container) => {
  // Custom logic for container operations
};
```

#### Document Operations

The `Doc` class provides comprehensive methods for CRUD operations:

**Loading Documents:**

```javascript
// Load a document using primary key fields
await customerDocLib.loadDocument();

// Load using a custom query
await customerDocLib.loadDocument({ customerId: "CUST-001" });
```

**Saving Documents:**

```javascript
// Save the entire document
await customerDocLib.save();

// Save only changed fields (delta)
await customerDocLib.saveDelta();

// Save with a specific query key (for update)
await customerDocLib.save(false, { customerId: "CUST-001" });
```

**Deleting Documents:**

```javascript
await customerDocLib.delete();
```

#### Working with Fields

The `Doc` class provides methods to manage fields:

```javascript
// Access fields by ID
const nameField = customerDocLib.f[customerNameField.id];

// Remove a field
customerDocLib.removeFieldWithId(customerNameField.id);

// Hide all fields
customerDocLib.hideAllFields();

// Make all fields optional
customerDocLib.optionalAllFields();
```

#### Auto-save Functionality

The `Doc` class includes built-in auto-save functionality:

```javascript
// Enable auto-save
customerDocLib.autoSave = true;

// Clear auto-save buffer
customerDocLib.clearAutoSaveBuffer();

// Generate auto-save buffer from doc
customerDocLib.generateAutoSaveBufferFromDoc();
```

#### User Interface Integration

The `Doc` class provides methods to interact with the UI:

```javascript
// Send document as response (create/update form)
customerDocLib.sendResponse();

// Send quick form response (modal view)
customerDocLib.sendQuickFormResponse();

// Send changes to specific fields
customerDocLib.sendChangeResponse([customerNameField, emailField]);
```

#### Document Building

The `Doc` class includes methods to construct documents from various sources:

```javascript
// Build from database record
customerDocLib.buildDocument(recordFromDB);

// Build document with sud-documents and sub-collections
customerDocLib.buildDocumentFromContainer(container);
```

### Collection Class

The `Collection` class manages groups of documents, implementing table-like structures that can display, filter, and interact with multiple document instances.

```javascript
import { Collection } from "@frontmltd/frontmjs/core/Collection";

export let customersCollectionLib = new Collection("customersCollectionLib", {
  title: "Customers",
  document: customerDocLib,
  allowClose: true,
  cache: false,
  shared: true,
  name: "customers",
  allowQuickActions: true,
  state,
});
customersCollectionLib.runOnCloud();
```

### Section Class

The `Section` class organises fields within a document, creating logical groupings that improve UI organisation and user experience.

```javascript
import { Section } from "@frontmltd/frontmjs/core/Section";

export let customerSectionLib = new Section("customerSectionLib", {
  title: "Customer",
  collapsable: false,
  defaultCollapsableState: false,
  columns: 2,
  doc: customerDocLib,
  state,
});
```

### Field Class

The `Field` class represents individual data points within a document, supporting various types and behaviours.

```javascript
import { Field } from "@frontmltd/frontmjs/core/Field";
import { FormFieldTypes } from "@frontmltd/frontmjs/core/FormFieldTypes";

export let customerNameLib = new Field("customerNameLib", {
  title: "Name",
  doc: customerDocLib,
  section: customerSectionLib,
  type: FormFieldTypes.TEXT_FIELD,
  primaryKey: true,
  mandatory: true,
  dbName: "customerName",
  column: 0,
  state,
});
```

## Building a Data Model: Step-by-Step Guide

### 1\. Creating a Document

Start by defining a document class that will represent your entity:

```javascript
import { Doc } from "@frontmltd/frontmjs/core/Doc";
import { D, state } from "@frontmltd/frontmjs/core/State";

export let customerDocLib = new Doc("customerDocLib", state, {
  title: "Customer", // Display title for the form
  confirm: "Save", // Label for submit button
  cancel: "Cancel", // Label for cancel button
  autoSave: true, // Enable automatic saving
});
customerDocLib.runOnCloud(); // Run the document logic on the cloud
```

### 2\. Implementing Document Event Handlers

Add event handlers to control document behaviour:

```javascript
customerDocLib.onSubmit = async () => {
  await customerDocLib.save();
  // Additional logic after saving
};
```

Common document events include:

- `onSubmit`: Triggered when the document is submitted from the UI

- `onLoad`: Triggered when the document is loaded

- `onSave`: Triggered before the document is saved

- `onPostLoad`: Triggered after document is loaded from database

- `onCancel`: Triggered when the document form is cancelled

- `onClose`: Triggered when the document form is closed

- `onPostBuild`: Triggered after building the document from data

### 3\. Creating a Collection

Define a collection to manage multiple document instances:

```javascript
import { Collection } from "@frontmltd/frontmjs/core/Collection";

export let customersCollectionLib = new Collection("customersCollectionLib", {
  title: "Customers", // Display title
  document: customerDocLib, // Associated document class
  allowClose: true, // Enable close button
  cache: false, // Disable caching
  shared: true, // Make collection shareable
  name: "customers", // Collection name in database
  allowQuickActions: true, // Enable quick actions
  state, // Application state
});
customersCollectionLib.runOnCloud();
```

The collection links to a document class and implements methods for loading, filtering, and manipulating document sets.

### 4\. How to load data into a collection

```javascript
const getCustomersList = async () => {
  const query = state.getField(MY_CUSTOMER_FIELD)
    ? { parentCustomerId: state.getField(MY_CUSTOMER_FIELD) }
    : { parentCustomerId: { $exists: false } };
  await customersCollectionLib.loadCollectionWithQuery({
    query,
    limit: 100,
  });
};
```

### 5\. Organising with Sections

Create sections to group related fields:

```javascript
import { Section } from "@frontmltd/frontmjs/core/Section";

export let customerSectionLib = new Section("customerSectionLib", {
  title: "Customer", // Section title
  collapsable: false, // Disable collapsing
  defaultCollapsableState: false, // Initial collapsed state
  columns: 2, // Number of columns in layout
  doc: customerDocLib, // Associated document
  state, // Application state
});
```

Sections improve UI organisation and user experience by grouping related fields together.

### 6\. Adding Fields to the Document

Define fields to capture data points within your document:

```javascript
import { Field } from "@frontmltd/frontmjs/core/Field";
import { FormFieldTypes } from "@frontmltd/frontmjs/core/FormFieldTypes";

export let customerIdLib = new Field("customerIdLib", {
  title: "Name", // Display label
  doc: customerDocLib, // Associated document
  type: FormFieldTypes.TEXT_FIELD, // Field type
  primaryKey: true, // Set as primary key
  hidden: true, // Hide from UI
  dbName: "customerId", // Database field name
  state, // Application state
});
```

Field types include:

- `TEXT_FIELD`: For text input

- `NUMBER_FIELD`: For numeric input

- `DATE`: For date selection

- `DROPDOWN`: For selecting from predefined options

### 7\. Organising Fields in Sections

Associate fields with sections for better organization:

```javascript
export let customerNameLib = new Field("customerNameLib", {
  title: "Name",
  doc: customerDocLib,
  section: customerSectionLib, // Link to parent section
  type: FormFieldTypes.TEXT_FIELD,
  primaryKey: true,
  mandatory: true,
  dbName: "customerName",
  column: 0, // Column position (0-based)
  state,
});
```

The `column` property controls the field's position within the section's layout.

### 8\. Implementing Field Behaviours

Add event handlers to control field behaviour:

```javascript
customerNameLib.onResponse = async (self) => {
  self.readOnly = state.getField(MY_CUSTOMER_FIELD) === customerIdLib.value;
};

mainContactEmail.onMoveOut = async () => {
  await validateEmailAddress(mainContactEmail);
};
```

Common field events include:

- `onInit`: Triggered when the field is initialised

- `onMoveOut`: Triggered when focus leaves the field

- `onResponse`: Triggered when preparing the field for display

- `onSave`: Triggered before the document is saved

## Advanced Data Modelling Techniques

### Primary Keys and Unique IDs

Implement automatic ID generation for new documents:

```javascript
customerIdLib.onInit = async (self) => {
  self.value = state.getUniqueId();
};
```

### Working with Sub-documents

The `Doc` class supports nested structures with sub-documents:

```javascript
// Create a child document
const addressDoc = new Doc("addressDoc", state, {
  title: "Address",
  autoSave: true,
  parentDoc: customerDocLib, // Link to parent document
});

// Add fields to the sub-document
const streetField = new Field("streetField", {
  title: "Street",
  doc: addressDoc,
  type: FormFieldTypes.TEXT_FIELD,
  dbName: "street",
  state,
});

// Check if a document has sub-documents
if (customerDocLib.hasSubDocs) {
  // Process sub-documents
}

// Access a specific child document
const address = customerDocLib.children.addressDoc;
```

### Document Security and Encryption

The `Doc` class provides methods for handling field encryption:

```javascript
// Create an encrypted field
const creditCardField = new Field("creditCardField", {
  title: "Credit Card",
  doc: customerDocLib,
  type: FormFieldTypes.TEXT_FIELD,
  encrypted: true, // Enable encryption
  dbName: "ccNumber",
  state,
});

// Get array of all encrypted fields in document
const securedFields = customerDocLib.getSecuredFieldsArray();
```

### Field Validation

Add validation to ensure data integrity:

```javascript
mainContactEmail.runOnCloud();
mainContactEmail.onMoveOut = async () => {
  await validateEmailAddress(mainContactEmail);
};
```

### Conditional Field Visibility

Control field visibility based on business rules (it could be done within onInit or onResponse):

```javascript
const hideFields = (self) => {
  self.hidden =
    state.getField(CURRENT_MICRO_APP_FIELD) !== SUPER_ADMIN_MICRO_APP ||
    !!parentCustomerIdLib.value;
  self.mandatory = !self.hidden;
};

accountNumber.onInit = async (self) => {
  hideFields(self);
};
```

### Dropdown Fields with Dynamic Options

Implement dropdown fields with dynamic options:

```javascript
export let customerStatus = new Field("customerStatus", {
  title: "Status",
  doc: customerDocLib,
  section: customerSectionLib,
  type: FormFieldTypes.DROPDOWN,
  column: 1,
  hiddenInTables: true,
  state,
  options: [ACTIVE_STATUS_LABEL, INACTIVE_STATUS_LABEL, SUSPENDED_STATUS_LABEL],
});
```

### Example: Complete Customer Document Implementation

```javascript
import { Doc } from "@frontmltd/frontmjs/core/Doc";
import { Field } from "@frontmltd/frontmjs/core/Field";
import { Section } from "@frontmltd/frontmjs/core/Section";
import { FormFieldTypes } from "@frontmltd/frontmjs/core/FormFieldTypes";
import { D, state } from "@frontmltd/frontmjs/core/State";

// Create the document
export let customerDocLib = new Doc("customerDocLib", state, {
  title: "Customer",
  confirm: "Save",
  cancel: "Cancel",
  autoSave: true,
  audit: true,
});
customerDocLib.runOnCloud();

// Create a section
export let customerSectionLib = new Section("customerSectionLib", {
  title: "Customer Details",
  collapsable: false,
  columns: 2,
  doc: customerDocLib,
  state,
});

// Add fields
export let customerIdLib = new Field("customerIdLib", {
  title: "Customer ID",
  doc: customerDocLib,
  section: customerSectionLib,
  type: FormFieldTypes.TEXT_FIELD,
  primaryKey: true,
  mandatory: true,
  dbName: "customerId",
  column: 0,
  state,
});

export let customerNameLib = new Field("customerNameLib", {
  title: "Name",
  doc: customerDocLib,
  section: customerSectionLib,
  type: FormFieldTypes.TEXT_FIELD,
  mandatory: true,
  dbName: "customerName",
  column: 1,
  state,
});

// Add event handlers
customerDocLib.onSubmit = async (self) => {
  await self.save();
  "Customer saved successfully".sendResponse();
};

customerDocLib.onPostLoad = async (self) => {
  if (!self.f.customerNameLib.value) {
    self.f.customerNameLib.value = "New Customer";
  }
};

customerIdLib.onInit = async (self) => {
  if (!self.value) {
    self.value = `CUST-${state.getUniqueId()}`;
  }
};

// Usage
const loadCustomer = async (customerId) => {
  await customerDocLib.loadDocument({ customerId });
  customerDocLib.sendResponse();
};

const saveCustomer = async () => {
  await customerDocLib.save();
  "Customer saved successfully".sendResponse();
};
```

## Conclusion

The [frontm.ai](http://frontm.ai) data modelling approach provides a powerful, flexible system for creating complex data structures with built-in UI generation and database operations. The Doc class, in particular, serves as the foundation of this system, allowing developers to define documents with rich behaviours and capabilities.

By following the patterns outlined in this guide, developers can quickly implement robust data models that leverage the full capabilities of the [frontm.ai](http://frontm.ai) framework while maintaining consistency with architectural principles. The unified approach using the `Intent` class hierarchy simplifies development while ensuring that all components work seamlessly together, from data entry forms to collection displays and custom operations.

This consistency is key to building maintainable maritime business applications that can evolve with changing requirements, providing both flexibility for developers and a cohesive experience for users.
