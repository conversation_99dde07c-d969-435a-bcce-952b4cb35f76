# frontm.ai high level description

[frontm.ai](http://frontm.ai) is an agentic architecture able to generate any sort of business software application from scratch based on simple user stories. The architecture today is trained to generate Maritime Business Applications since it is a niche market with less competition involved.

In [frontm.ai](http://frontm.ai) there is one single class used to represent all elements in the architecture. The class is called `Intent`.

The `Intent` class is used to represent data in MongoDB. It is extended to represent collections with the `Collection` class, Documents with the `Docs` class and Fields with the `Field` class. The `Intent` class can implement relationships with itself to create complex data structures.

The `Intent` class is used to represent elements in a User Interface. The `Colletion` class is used to represent Tables, Maps and Calendars. The `Doc` class represent forms, rows in tables, markers and route points on a map and calendar entries. The `Field` class represent fields on a form, columns in a table, marker or calendar entry attributes.

The data sctructures implemented by the `Intent` class can be represented by JSON objects representing data that can be transmitted through the architecture. This structure create the basis for the DOM on a web page for instance and mixed with CSS classes and react.js components, it can implement any sort of user interface.

The architecture user interfaces are all implemented in react.js and even when they run within mobile apps, the UI is dynamicaly generated by react.js.

The [frontm.ai](http://frontm.ai) framework implements a micro-services architecture where the `Intent` class implements the business logic of the services and sends the responses to the callers which can be users, external systems or other [frontm.ai](http://frontm.ai) intents.

The `Intent` class implements interfaces creating standard behaviours within the entire architecture. So when we implement a datamodel in [frontm.ai](http://frontm.ai), we automatically implement a user interface and database operations without developers having to worry about the details. These behaviours can also be overriden and replaced by custom logic. The look & feel of the UI can be standard, or it can be overridden with a layer on top.

When a message arrives at the [frontm.ai](http://frontm.ai) system, the routing system called `intent matching` kicks in and decides to which micro-service the message is addressed. This is done analysing the content of the message implementing rules for each of the intents or using AI Models that will determine which Intent will need to process the answer.

Each micro-service is independent from the other, enabling a full decoupling for each service. When micro-services need to call each other, they do it through messaging, or if it happens within the same conversation and session, there is a state implementing a short-lasting memory that all Intents have access to. The flow of messages between the micro-services and external actors implement a graph where each Intent object is a node.

The standard classes like `Collection`, `Doc` and `Field` have a set of standard rules pre-defined that will route the requests to the appropriate code. This mechanism works for a user clicking on a button, a user tabbing out a field, an external system calling an API end-point or a natural language question sent by a user.

Each instance of the `Intent` class implements a semantic meaning, which means that entire architecture can be described clearly in natural language and developed fully.

The consequence of this model is that each request that comes to the system can also be represented in natural language, and so the system generates a response and what the system has done to obtain it.

When a user or an external application sends requests to the system, [frontm.ai](http://frontm.ai) generates a unique session identification called `conversation`. All the messages exchanged between the external user or system are stored, creating a story, a User Story of the messages. Since all the components involved can be represented in natural language, the user stories can also be represented like that. These User Stories can then be used to train LLMs that can create code on the flight, implementing an agentic architecture in this way.

Today, the platform is implemented in node.js running on AWS lambdas. The intents are implemented as small units of code called micro-apps, which can all be accessed from the FrontM super-app. The goal of the small pieces of code within the `Intent` objects is to minimise hallucinations since LLMs are much more precise generating short snippets.
The Javascript system will evolve in the future to Typescript and lastly to Rust. Since in our view typed language are more precise reducing the potential number of runtime errors.

The architecture is multi-tenanted using databases in MongoDB Atlas and Cloude Sonnet called from AWS Bedrock.

As a summary, this is how [frontm.ai](http://frontm.ai) resolves the four stages of an agentic system:

1. perceive (gather data): The multi-tenanted architecture implemented in AWS creates separated databases that connected to the Bedrock LLM models can implement a very powerful RAG system. The databases use MongoDB Atlas data federation to receive data from multiple sources.

2. reason (use LLMs as orchestrators): The [frontm.ai](http://frontm.ai) `intent matching` stage using a combination of fixed rules and AI to route requests acts a workflow orchestrator.

3. Act (execute tasks via APIs): The architecture is not only able to generate code to call APIs, using the past history of conversations and millions of [frontm.ai](http://frontm.ai) training snippets, this architecture can generate the code to resolve the requests by itself without depending on third party costly APIs.

4. Learn (improve through feedback): The ability to capture and recreate User Stories within conversations allows the architecture to learn from past user behavior and improve itself.
