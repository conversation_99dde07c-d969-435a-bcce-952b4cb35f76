# frontmLib Documentation

## Overview

The FrontM Library (`frontmlib`) is a comprehensive JavaScript library that provides a wide range of utilities and functions for interacting with AWS services, managing users, handling messages, and performing various database operations. This library serves as the foundation for FrontM's backend services and applications.

## Modules

The documentation is organized into the following modules:

1. **Database Operations** - Functions for interacting with DynamoDB and other database services
2. **User Management** - Functions for creating, retrieving, updating, and deleting users
3. **Messaging** - Functions for handling messages and conversations
4. **AWS Services** - Functions for interacting with various AWS services (Lambda, S3, SNS, SQS, etc.)
5. **Utility Functions** - Helper functions and utilities for common tasks
6. **Constants and Enums** - Exported constants and enumerations used throughout the library

## Installation

The FrontM Library is typically included as a dependency in FrontM projects. It can be found within the state when the intents run on cloud:

```javascript
// Import the entire library
const frontmlib = state.frontmlib;
```

## Common Usage Patterns

### Working with Users

```javascript
// Get a user by ID
const user = await frontmlib.getUser("user123");

// Create a new user
const newUser = {
  userId: frontmlib.getUniqueId(),
  userName: "John Doe",
  emailAddress: "<EMAIL>",
  domains: ["example.com"],
  domainsSearch: ["example.com"],
};
const result = await frontmlib.createUser(newUser, []);

// Update a user
const updateResult = await frontmlib.updateUser(
  { userName: "John Smith" },
  { userId: "user123" },
);

// Delete a user
const deleteResult = await frontmlib.deleteUser("user123");
```

### Working with Messages

```javascript
// Get messages from a conversation
const messages = await frontmlib.getMessages("conv123");

// Insert new messages
const newMessages = [
  {
    messageId: "msg123",
    conversationId: "conv123",
    createdOn: Date.now(),
    createdBy: "user123",
    contentType: "100",
    content: "Hello, world!",
    ttl: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60, // 30 days from now
  },
];
const insertResult = await frontmlib.insertMessages(newMessages);

// Create a conversation ID
const conversationId = frontmlib.createConversationId("user123", "bot456");
```

### Working with AWS Services

```javascript
// Invoke a Lambda function
const lambdaResult = await frontmlib.invokeLambda(
  "ProcessData",
  frontmlib.LAMBDA_INVOCATION_TYPES.REQUEST_RESPONSE,
  { data: "example" },
);

// Get a signed URL for S3
const url = await frontmlib.getS3SignedUrl(
  "my-bucket",
  "path/to/file.pdf",
  3600, // 1 hour
);

// Upload to S3
const uploadResult = await frontmlib.uploadToS3Bucket({
  bucket: "my-bucket",
  key: "uploads/file.txt",
  body: "This is the content of the file.",
});

// Send a notification
const notificationResult = await frontmlib.publishNotificationToSNS({
  TopicArn: "arn:aws:sns:us-east-1:123456789012:MyTopic",
  Subject: "Notification",
  Message: "This is a test notification.",
});
```

### Working with Database

```javascript
// Get an item by key
const item = await frontmlib.dbGetWithKey("MyTable", { id: "123" }, [
  "field1",
  "field2",
]);

// Query with an index
const items = await frontmlib.dbGetWithIndex(
  "MyTable",
  "myIndex",
  "attribute = :value",
  { ":value": "example" },
);

// Put an item
const putResult = await frontmlib.dbPutItem("MyTable", {
  id: "123",
  name: "Example",
  value: 42,
});

// Update an item
const updateResult = await frontmlib.dbUpdateItem(
  "MyTable",
  { id: "123" },
  "SET #name = :name, #value = :value",
  { ":name": "New Name", ":value": 99 },
  { "#name": "name", "#value": "value" },
);
```

## Error Handling

Most functions in the FrontM Library return objects with an `error` property when an error occurs. It's recommended to check for this property before proceeding with the result:

```javascript
const result = await frontmlib.dbGetWithKey("MyTable", { id: "123" });
if (result && result.error) {
  console.error("Error:", result.error);
  // Handle the error
} else {
  // Process the result
  console.log("Item:", result);
}
```

Some functions may throw exceptions instead, particularly for validation errors. It's good practice to wrap these calls in try-catch blocks:

```javascript
try {
  const encryptedData = await frontmlib.encryptData(keyArn, sensitiveData);
  // Use the encrypted data
} catch (error) {
  console.error("Encryption error:", error.message);
  // Handle the error
}
```

## Logging

The FrontM Library provides several functions for logging:

```javascript
// Log an informational message
await frontmlib.addLogEntry(
  "MyService",
  { level: "INFO", message: "Operation completed successfully" },
  { userId: "user123" },
);

// Log performance metrics
const startTime = Date.now();
// Perform some operation
const result = await someOperation();
await frontmlib.addPerfLogEntry(
  "MyService",
  { message: "Operation performance", startTime },
  { userId: "user123" },
);

// Log user activity
await frontmlib.logActivity({
  userId: "user123",
  userEmail: "<EMAIL>",
  domain: "example.com",
  timestamp: Date.now(),
  // Other activity data
});
```

## Security

The FrontM Library includes functions for handling sensitive data securely:

```javascript
// Encrypt data
const encryptedData = await frontmlib.encryptData(
  "arn:aws:kms:us-east-1:123456789012:key/abcd-1234",
  sensitiveData,
);

// Decrypt data
const decryptedData = await frontmlib.decryptData(encryptedData);

// Get a secret
const secret = await frontmlib.getSecret("my-secret-name");
```

## Conclusion

The FrontM Library provides a rich set of functions and utilities for building robust applications on AWS. For detailed information about specific functions, please refer to the module documentation linked above.
