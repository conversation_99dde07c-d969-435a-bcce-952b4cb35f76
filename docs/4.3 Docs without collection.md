# Using Docs Without Collections in frontm.ai

## Overview

In frontm.ai, while Docs and Collections often work together, it's perfectly valid and sometimes preferable to create a Doc without an associated Collection. This approach is useful in specific scenarios where persistence is not required.

## Use Cases

### UI-Only Forms

The primary use case is creating forms that exist solely for UI presentation purposes:

- **Information Display Forms**: Like `allInformationDoc`, which serves as a container for displaying information without needing to be saved
- **Configuration Panels**: Forms that modify settings in memory or trigger actions without persisting themselves

### Benefits

- **Reduced Overhead**: No database operations means faster performance
- **Simplified Architecture**: No need to manage collection loading/saving logic
- **Memory Efficiency**: Temporary data structures that don't consume database resources

## Implementation Example

```javascript
// Create a Doc without an associated Collection
export const displayOnlyDoc = new Doc("displayOnlyDoc", state, {
  title: "Information Display",
  description: "View-only information panel",
  readOnly: true, // Optional: make it read-only
});
displayOnlyDoc.runOnCloud();

// Create a section for the form
export const displaySection = new Section("displaySection", {
  title: "Information Overview",
  collapsable: false,
  columns: 1,
  doc: displayOnlyDoc,
  forCollection: true,
  state,
});

// Create a collection that will populate the display
export const dataCollection = new Collection("dataCollection", {
  title: "Data Summary",
  document: someOtherDoc, // This is the document that defines the data structure
  allowSearch: true,
  allowRefresh: true,
  state,
});
dataCollection.runOnCloud();

// Add the collection to the section
displaySection.addCollection(dataCollection);

// Function to load data and send the response to UI
export const loadDisplayInformation = async (currentPage = 1) => {
  // Count total pages for pagination
  await dataCollection.countTotalPages({
    query: {},
    limit: 10,
  });

  // Load the collection data
  await dataCollection.loadCollectionWithQuery({
    query: {},
    limit: 10,
    currentPage,
  });

  // Send the Doc (not the Collection) as the response
  // This is key - we're sending the Doc that contains the Collection
  displayOnlyDoc.sendResponse();
};

// Example menu entry click handler
menuEntry.onClick = async () => {
  await loadDisplayInformation();
};
```
