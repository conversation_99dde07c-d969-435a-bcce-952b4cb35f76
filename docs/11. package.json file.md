# This is the standard package.json file for all frontm.ai micro-apps

```json
{
  "version": "1.0.0",
  "name": "vikand-clinician-portal",
  "devDependencies": {
    "@babel/core": "^7.27.4",
    "@babel/preset-env": "^7.27.2",
    "@webpack-cli/generators": "^3.0.7",
    "babel-loader": "^10.0.0",
    "eslint": "^8.57.1",
    "prettier": "^3.5.3",
    "webpack": "^5.99.9",
    "webpack-cli": "^6.0.1"
  },
  "bin": {
    "frontm-build": "./node_modules/@frontmltd/frontmjs/after.js"
  },
  "scripts": {
    "lint": "eslint src --max-warnings=100",
    "build": "npm run lint && npm run format && webpack --mode=production --node-env=production && npm exec frontm-build",
    "build:dev": "npm run lint && npm run format && webpack --mode=development && npm exec frontm-build",
    "build:prod": "npm run lint && npm run format && webpack --mode=production --node-env=production && npm exec frontm-build",
    "format": "prettier --write .",
    "watch": "webpack --watch"
  },
  "dependencies": {
    "@frontmltd/frontmjs": "^4.4.24"
  }
}
```
