# User Management

## Table of Contents

- createUser
- createUserWithTtl
- getUserFieldsByUserId
- getUsersByDbQuery
- getUser
- getUserByEmail
- updateUser
- deleteUser
- updateUserLastActivity
- getActiveUsersList

## createUser

### Function Signature

```javascript
async createUser(dbUser, dateFields)
```

### Description

Creates a new user in the system database. This function inserts a user document into the People collection and updates related caches and search indexes. It handles secure fields by encrypting sensitive information and ensures the user data is properly stored and indexed.

### Parameters

| Name       | Type          | Required | Default | Description                                                |
| ---------- | ------------- | -------- | ------- | ---------------------------------------------------------- |
| dbUser     | object        | Yes      | \-      | The user object to create                                  |
| dateFields | Array<string> | Yes      | \-      | Array of field names that should be treated as date fields |

### Return Value

- **Type**: Object
- **Description**: The response from the database operation

### Usage Examples

#### Basic Example

```javascript
const newUser = {
  userId: "user123",
  userName: "John Doe",
  emailAddress: "<EMAIL>",
  domains: ["example.com"],
  domainsSearch: ["example.com"],
};

const result = await createUser(newUser, []);
// Creates a new user and returns the operation result
```

#### With Date Fields

```javascript
const newUser = {
  userId: "user456",
  userName: "Jane Smith",
  emailAddress: "<EMAIL>",
  domains: ["example.com"],
  domainsSearch: ["example.com"],
  createdAt: Date.now(),
  lastLogin: Date.now(),
};

const result = await createUser(newUser, ["createdAt", "lastLogin"]);
// Creates a new user with date fields properly formatted
```

### Error Handling

If the database operation fails, the function will return the error response from the MongoDB manager. Check the statusCode property to determine if the operation was successful (200) or failed.

### Related Functions

- [createUserWithTtl](http://#createuserwithttl)
- [updateUser](http://#updateuser)
- [deleteUser](http://#deleteuser)

## createUserWithTtl

### Function Signature

```javascript
async createUserWithTtl(dbUser, ttl)
```

### Description

Creates a new user with a time-to-live (TTL) value. This function is a specialized version of createUser that adds an expiration time to the user document, making it automatically expire after the specified time period. This is particularly useful for creating temporary users.

### Parameters

| Name   | Type   | Required | Default | Description                   |
| ------ | ------ | -------- | ------- | ----------------------------- |
| dbUser | object | Yes      | \-      | The user object to create     |
| ttl    | number | Yes      | \-      | Time-to-live value in seconds |

### Return Value

- **Type**: Object
- **Description**: The response from the database operation

### Usage Examples

#### Basic Example

```javascript
const tempUser = {
  userId: "temp123",
  userName: "Temporary User",
  emailAddress: "<EMAIL>",
  domains: ["example.com"],
  domainsSearch: ["example.com"],
};

// Create a user that expires in 24 hours
const oneDay = 24 * 60 * 60; // 24 hours in seconds
const result = await createUserWithTtl(tempUser, oneDay);
```

#### Creating a Short-lived Guest User

```javascript
const guestUser = {
  userId: "guest789",
  userName: "Guest User",
  emailAddress: "<EMAIL>",
  domains: ["example.com"],
  domainsSearch: ["example.com"],
  tempUser: true,
};

// Create a guest user that expires in 1 hour
const oneHour = 60 * 60; // 1 hour in seconds
const result = await createUserWithTtl(guestUser, oneHour);
```

### Error Handling

If the database operation fails, the function will return the error response from the MongoDB manager. Check the statusCode property to determine if the operation was successful (200) or failed.

### Related Functions

- [createUser](http://#createuser)
- [deleteUser](http://#deleteuser)

## getUserFieldsByUserId

### Function Signature

```javascript
async getUserFieldsByUserId(userId, fields = [])
```

### Description

Retrieves specific fields of a user by their user ID. This utility function allows you to fetch only the fields you need from a user document, optimizing data transfer and processing. If no fields are specified, it returns the complete user document.

### Parameters

| Name   | Type          | Required | Default | Description                                                    |
| ------ | ------------- | -------- | ------- | -------------------------------------------------------------- |
| userId | string        | Yes      | \-      | The ID of the user to retrieve                                 |
| fields | Array<string> | No       | \[\]    | Array of field names to retrieve; if empty, returns all fields |

### Return Value

- **Type**: Object
- **Description**: The user object with the requested fields or the complete user object if no fields were specified

### Usage Examples

#### Basic Example - Get All Fields

```javascript
const user = await getUserFieldsByUserId("user123");
// Returns the complete user object
```

#### Get Specific Fields

```javascript
const userProfile = await getUserFieldsByUserId("user123", [
  "userId",
  "userName",
  "emailAddress",
]);
// Returns only the userId, userName, and emailAddress fields
```

### Error Handling

If the user is not found, the function throws an error with the message: `User with [userId] not found.`

### Related Functions

- [getUser](http://#getuser)
- [getUserByEmail](http://#getuserbyemail)
- [getUsersByDbQuery](http://#getusersbydbquery)

## getUsersByDbQuery

### Function Signature

```javascript
async getUsersByDbQuery(query, projection = {}, sort = {}, collation = {}, skip = 0, limit = 1)
```

### Description

Retrieves users from the database based on a custom query. This function provides a flexible way to search for users using MongoDB query syntax, with support for projections, sorting, collation, pagination, and more. It's the foundation for many other user retrieval functions.

### Parameters

| Name       | Type   | Required | Default | Description                                 |
| ---------- | ------ | -------- | ------- | ------------------------------------------- |
| query      | object | Yes      | \-      | MongoDB query object to filter users        |
| projection | object | No       | {}      | Fields to include or exclude in the results |
| sort       | object | No       | {}      | Sorting criteria for the results            |
| collation  | object | No       | {}      | Collation options for string comparison     |
| skip       | number | No       | 0       | Number of documents to skip                 |
| limit      | number | No       | 1       | Maximum number of documents to return       |

### Return Value

- **Type**: Array<Object>
- **Description**: An array of user objects matching the query

### Usage Examples

#### Basic Example - Find User by ID

```javascript
const users = await getUsersByDbQuery({ userId: "user123" });
// Returns an array with the user whose ID is 'user123'
```

#### Advanced Example - Find Active Users in a Domain

```javascript
const activeUsers = await getUsersByDbQuery(
  {
    domains: "example.com",
    status: "active",
  },
  { userId: 1, userName: 1, emailAddress: 1 }, // Projection
  { userName: 1 }, // Sort by userName ascending
  {}, // No collation
  0, // Skip none
  50, // Limit to 50 results
);
// Returns up to 50 active users in the example.com domain,
// sorted by userName, with only the specified fields
```

### Error Handling

If the database operation fails, the function will return the error response from the MongoDB manager. Check the response structure to determine if the operation was successful.

### Related Functions

- [getUser](http://#getuser)
- [getUserByEmail](http://#getuserbyemail)
- [getUserFieldsByUserId](http://#getuserfieldsbyuserid)

## getUser

### Function Signature

```javascript
async getUser(userId)
```

### Description

Retrieves a user by their user ID, with caching support. This function first attempts to retrieve the user from the cache for faster access. If the user is not found in the cache, it falls back to retrieving from the database and then updates the cache for future requests.

### Parameters

| Name   | Type   | Required | Default | Description                    |
| ------ | ------ | -------- | ------- | ------------------------------ |
| userId | string | Yes      | \-      | The ID of the user to retrieve |

### Return Value

- **Type**: Object
- **Description**: The user object or an empty object if not found

### Usage Examples

#### Basic Example

```javascript
const user = await getUser("user123");
// Returns the user object if found, or an empty object if not found
```

#### Checking if User Exists

```javascript
const user = await getUser("user123");
if (Object.keys(user).length > 0) {
  console.log("User found:", user.userName);
} else {
  console.log("User not found");
}
```

### Error Handling

If the userId is empty or undefined, the function returns an empty object. If there's an error retrieving the user, it will be logged to the console, but the function will still return whatever data it could retrieve.

### Related Functions

- [getUserByEmail](http://#getuserbyemail)
- [getUserFieldsByUserId](http://#getuserfieldsbyuserid)
- [updateUser](http://#updateuser)

## getUserByEmail

### Function Signature

```javascript
async getUserByEmail(email)
```

### Description

Retrieves a user by their email address, with caching support. Similar to getUser, this function first checks the cache for faster access, then falls back to the database if needed, and updates the cache for future requests.

### Parameters

| Name  | Type   | Required | Default | Description                               |
| ----- | ------ | -------- | ------- | ----------------------------------------- |
| email | string | Yes      | \-      | The email address of the user to retrieve |

### Return Value

- **Type**: Object
- **Description**: The user object or an empty object if not found

### Usage Examples

#### Basic Example

```javascript
const user = await getUserByEmail("<EMAIL>");
// Returns the user object if found, or an empty object if not found
```

#### Checking User Domain

```javascript
const user = await getUserByEmail("<EMAIL>");
if (Object.keys(user).length > 0) {
  const domains = user.domains || [];
  if (domains.includes("example.com")) {
    console.log("User belongs to example.com domain");
  }
} else {
  console.log("User not found");
}
```

### Error Handling

If the email is empty or undefined, the function returns an empty object. If there's an error retrieving the user, it will be logged to the console, but the function will still return whatever data it could retrieve.

### Related Functions

- [getUser](http://#getuser)
- [getUserFieldsByUserId](http://#getuserfieldsbyuserid)
- [updateUser](http://#updateuser)

## updateUser

### Function Signature

```javascript
async updateUser(document, query, updateCache = true)
```

### Description

Updates a user in the database and optionally refreshes the cache. This function allows you to modify user attributes by providing the update document and a query to identify the user. It also handles updating the search index and can refresh the user's cache entries.

### Parameters

| Name        | Type    | Required | Default | Description                                                    |
| ----------- | ------- | -------- | ------- | -------------------------------------------------------------- |
| document    | object  | Yes      | \-      | The update document with fields to modify                      |
| query       | object  | Yes      | \-      | Query to identify the user to update (e.g., { userId: '123' }) |
| updateCache | boolean | No       | true    | Whether to update the user's cache entries                     |

### Return Value

- **Type**: Object
- **Description**: The response from the database operation

### Usage Examples

#### Basic Example

```javascript
const updateResult = await updateUser(
  { userName: "New Name", lastUpdated: Date.now() },
  { userId: "user123" },
);
// Updates the userName and lastUpdated fields for the user
```

#### Update Without Refreshing Cache

```javascript
const updateResult = await updateUser(
  { loginCount: 42 },
  { userId: "user123" },
  false, // Don't update cache
);
// Updates the loginCount field but doesn't refresh the cache
```

### Error Handling

If the database operation fails, the function will return the error response from the MongoDB manager. Check the statusCode property to determine if the operation was successful (200) or failed.

### Related Functions

- [getUser](http://#getuser)
- [getUserByEmail](http://#getuserbyemail)
- [createUser](http://#createuser)
- [deleteUser](http://#deleteuser)

## deleteUser

### Function Signature

```javascript
async deleteUser(userId)
```

### Description

Deletes a user from the system. This function removes the user from the database, clears their cache entries, and updates the search index to reflect the deletion. It ensures that all traces of the user are properly removed from the system.

### Parameters

| Name   | Type   | Required | Default | Description                  |
| ------ | ------ | -------- | ------- | ---------------------------- |
| userId | string | Yes      | \-      | The ID of the user to delete |

### Return Value

- **Type**: Object
- **Description**: The response from the database operation

### Usage Examples

#### Basic Example

```javascript
const deleteResult = await deleteUser("user123");
// Deletes the user with ID 'user123'
```

### Error Handling

If the user is not found, the function throws an error with the message: `Invalid user: [userId]`. If there's an error during the deletion process, it will be logged to the console, and the function will return the error response from the MongoDB manager.

### Related Functions

- [createUser](http://#createuser)
- [updateUser](http://#updateuser)
- [getUser](http://#getuser)

## updateUserLastActivity

### Function Signature

```javascript
async updateUserLastActivity(userId, lastActivityName, userDomain, location = "Cloud")
```

### Description

Records a user's activity in the system. This function adds an entry to the user activity index, tracking what operations users perform and when. This data can be used for analytics, auditing, and monitoring user engagement. Note that it does not update activity for temporary users.

### Parameters

| Name             | Type   | Required | Default | Description                                           |
| ---------------- | ------ | -------- | ------- | ----------------------------------------------------- |
| userId           | string | Yes      | \-      | The ID of the user                                    |
| lastActivityName | string | Yes      | \-      | Name of the activity or operation performed           |
| userDomain       | string | Yes      | \-      | The domain the user belongs to                        |
| location         | string | No       | "Cloud" | Where the activity occurred (e.g., "Cloud", "Mobile") |

### Return Value

- **Type**: void
- **Description**: No return value

### Usage Examples

#### Basic Example

```javascript
await updateUserLastActivity("user123", "LOGIN", "example.com");
// Records a login activity for the user
```

#### Specifying Location

```javascript
await updateUserLastActivity(
  "user123",
  "MESSAGE_SENT",
  "example.com",
  "Mobile App",
);
// Records a message sending activity from the mobile app
```

### Error Handling

If the user is a temporary user (has the tempUser flag set to true), the function will silently return without recording the activity. If there's an error adding the document to the index, it will be logged to the console.

### Related Functions

- [getActiveUsersList](http://#getactiveuserslist)
- [addDocToESIndex](http://#addDocToESIndex)

## getActiveUsersList

### Function Signature

```javascript
async getActiveUsersList({ startTime = ONE_DAY_AGO, endTime = Date.now(), domain })
```

### Description

Retrieves a list of users who have been active within a specified time period and domain. This function queries the activity records to find unique users who have performed any action in the system during the given time range, providing insights into user engagement and activity patterns.

### Parameters

| Name              | Type   | Required | Default      | Description                             |
| ----------------- | ------ | -------- | ------------ | --------------------------------------- |
| options           | object | Yes      | \-           | Options object                          |
| options.startTime | number | No       | 24 hours ago | Start timestamp for the activity period |
| options.endTime   | number | No       | Current time | End timestamp for the activity period   |
| options.domain    | string | Yes      | \-           | The domain to filter activities by      |

### Return Value

- **Type**: Array<Object>
- **Description**: An array of unique user objects with userId and userEmail properties

### Usage Examples

#### Basic Example

```javascript
const activeUsers = await getActiveUsersList({
  domain: "example.com",
});
// Returns all users active in the last 24 hours in the example.com domain
```

#### Custom Time Range

```javascript
const lastWeekStart = Date.now() - 7 * 24 * 60 * 60 * 1000;
const lastWeekEnd = Date.now() - 1 * 24 * 60 * 60 * 1000;

const lastWeekActiveUsers = await getActiveUsersList({
  startTime: lastWeekStart,
  endTime: lastWeekEnd,
  domain: "example.com",
});
// Returns users active during the previous week in the example.com domain
```

### Error Handling

If there's an error during the database query, it will be logged to the console, and the function may return an empty array or partial results.

### Related Functions

- [updateUserLastActivity](http://#updateuserlastactivity)

## Verification Checklist

- ✅ All exported user management functions are documented
- ✅ All parameters are explained
- ✅ All return values are described
- ✅ Examples demonstrate proper usage
- ✅ No sections are incomplete or marked as "TODO"
