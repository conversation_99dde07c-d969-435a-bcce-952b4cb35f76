# AWS Services

## Table of Contents

- Lambda Functions
  - invokeLambda
- Secrets Management
  - getSecret
- Encryption
  - encryptData
  - decryptData
- SNS Notifications
  - publishNotificationToSNS
- S3 Operations
  - getS3SignedUrl
  - getS3UploadSignedUrl
  - uploadToS3Bucket
  - copyS3Object
  - getS3Object
  - getS3ObjectAsStream

## Lambda Functions

### invokeLambda

#### Function Signature

```javascript
async invokeLambda(functionName, invocationType, jsonPayload)
```

#### Description

Invokes an AWS Lambda function with the specified payload. This function supports both synchronous (RequestResponse) and asynchronous (Event) invocation types, allowing for flexible integration with other Lambda functions.

#### Parameters

| Name           | Type   | Required | Default | Description                                                                      |
| -------------- | ------ | -------- | ------- | -------------------------------------------------------------------------------- |
| functionName   | string | Yes      | \-      | The name of the Lambda function to invoke                                        |
| invocationType | string | Yes      | \-      | The invocation type: "RequestResponse" for synchronous, "Event" for asynchronous |
| jsonPayload    | object | Yes      | \-      | The payload to send to the Lambda function                                       |

#### Return Value

- **Type**: Object or null
- **Description**: For synchronous invocations, returns the parsed response from the Lambda function. For asynchronous invocations, returns the raw Lambda response object. Returns null if an error occurs.

#### Usage Examples

##### Synchronous Invocation

```javascript
const result = await invokeLambda(
  "ProcessOrder",
  LAMBDA_INVOCATION_TYPES.REQUEST_RESPONSE,
  { orderId: "12345", items: [{ productId: "P1", quantity: 2 }] },
);
// Returns the processed result from the Lambda function
```

##### Asynchronous Invocation

```javascript
const response = await invokeLambda(
  "SendNotification",
  LAMBDA_INVOCATION_TYPES.EVENT,
  { userId: "user123", message: "Your order has shipped!" },
);
// Triggers the Lambda function but doesn't wait for the result
```

#### Error Handling

If an error occurs during the Lambda invocation, the function logs the error to the console but doesn't throw an exception, returning null instead.

#### Related Functions

- [createUser](http://#createuser)
- [updateUser](http://#updateuser)
- [getUsersByDbQuery](http://#getusersbydbquery)

## Secrets Management

### getSecret

#### Function Signature

```javascript
async getSecret(secretName)
```

#### Description

Retrieves a secret from AWS Secrets Manager. This function is used to securely access sensitive information such as API keys, database credentials, and other configuration secrets without hardcoding them in the application code.

#### Parameters

| Name       | Type   | Required | Default | Description                               |
| ---------- | ------ | -------- | ------- | ----------------------------------------- |
| secretName | string | Yes      | \-      | The name or ARN of the secret to retrieve |

#### Return Value

- **Type**: Object or String or Error Object
- **Description**: The secret value as a parsed JSON object if it's a JSON string, or as a string if it's a binary secret. Returns an error object if the operation fails.

#### Usage Examples

##### Retrieving Database Credentials

```javascript
const dbCredentials = await getSecret("database/production");
// Returns an object like: { username: 'dbuser', password: 'dbpass', host: 'db.example.com' }

// Use the credentials
const connection = await connectToDatabase(
  dbCredentials.host,
  dbCredentials.username,
  dbCredentials.password,
);
```

##### Retrieving API Keys

```javascript
const apiKeys = await getSecret("external-apis/payment-gateway");
// Returns an object like: { apiKey: 'key123', apiSecret: 'secret456' }

// Use the API keys
const paymentClient = new PaymentGatewayClient(
  apiKeys.apiKey,
  apiKeys.apiSecret,
);
```

#### Error Handling

If an error occurs while retrieving the secret, the function returns an error object with the format: `{ error: "error message" }`.

#### Related Functions

- [encryptData](http://#encryptdata)
- [decryptData](http://#decryptdata)

## Encryption

### encryptData

#### Function Signature

```javascript
async encryptData(keyArn, data)
```

#### Description

Encrypts data using AWS Key Management Service (KMS). This function provides a secure way to encrypt sensitive information before storing it, ensuring that the data can only be decrypted by authorized entities with access to the KMS key.

#### Parameters

| Name   | Type             | Required | Default | Description                                                 |
| ------ | ---------------- | -------- | ------- | ----------------------------------------------------------- |
| keyArn | string           | Yes      | \-      | The ARN of the KMS key to use for encryption                |
| data   | string or object | Yes      | \-      | The data to encrypt (objects are automatically stringified) |

#### Return Value

- **Type**: Buffer
- **Description**: A buffer containing the encrypted data

#### Usage Examples

##### Encrypting a String

```javascript
const encryptedData = await encryptData(
  "arn:aws:kms:us-east-1:123456789012:key/abcd-1234-efgh-5678",
  "This is sensitive information",
);
// Returns a buffer with the encrypted data
```

##### Encrypting an Object

```javascript
const userData = {
  ssn: "***********",
  creditCard: "4111-1111-1111-1111",
  expiryDate: "12/25",
};

const encryptedData = await encryptData(
  "arn:aws:kms:us-east-1:123456789012:key/abcd-1234-efgh-5678",
  userData,
);
// The object is automatically stringified before encryption
```

#### Error Handling

The function throws an error in the following cases:

- If the data is empty, undefined, or not a string or object: `Invalid value for encrypting`
- If the keyArn is empty: `key required for encrypting data`

#### Related Functions

- [decryptData](http://#decryptdata)
- [getSecret](http://#getsecret)

### decryptData

#### Function Signature

```javascript
async decryptData(data)
```

#### Description

Decrypts data that was encrypted using AWS Key Management Service (KMS). This function is the counterpart to encryptData and is used to recover the original plaintext from encrypted data.

#### Parameters

| Name | Type   | Required | Default | Description                   |
| ---- | ------ | -------- | ------- | ----------------------------- |
| data | Buffer | Yes      | \-      | The encrypted data to decrypt |

#### Return Value

- **Type**: String or Object
- **Description**: The decrypted data as a string, or as an object if the decrypted string is valid JSON

#### Usage Examples

##### Decrypting to a String

```javascript
// Assuming encryptedData is a buffer from a previous encryption
const decryptedData = await decryptData(encryptedData);
// Returns the original plaintext string
```

##### Decrypting to an Object

```javascript
// Assuming encryptedData contains an encrypted JSON string
const decryptedObject = await decryptData(encryptedData);
// If the decrypted data is valid JSON, it's automatically parsed into an object
```

#### Error Handling

The function throws an error in the following cases:

- If the data is empty or undefined: `Invalid value for decrypting`
- If there's an error during decryption: The error message from KMS

#### Related Functions

- [encryptData](http://#encryptdata)
- [getSecret](http://#getsecret)

## SNS Notifications

### publishNotificationToSNS

#### Function Signature

```javascript
async publishNotificationToSNS({ TopicArn, TargetArn, Subject, Message, MessageStructure, MessageAttributes })
```

#### Description

Publishes a notification to an Amazon SNS topic or target. This function is used to send notifications to subscribers, which can include email addresses, SMS numbers, HTTP/HTTPS endpoints, and other AWS services like SQS queues or Lambda functions.

#### Parameters

| Name                      | Type   | Required | Default | Description                                           |
| ------------------------- | ------ | -------- | ------- | ----------------------------------------------------- |
| options                   | object | Yes      | \-      | Options object                                        |
| options.TopicArn          | string | No\*     | \-      | The ARN of the SNS topic to publish to                |
| options.TargetArn         | string | No\*     | \-      | The ARN of the specific target to publish to          |
| options.Subject           | string | No       | \-      | The subject of the notification (for email protocols) |
| options.Message           | string | Yes      | \-      | The message content                                   |
| options.MessageStructure  | string | No       | \-      | The structure of the message (e.g., "json")           |
| options.MessageAttributes | object | No       | \-      | Additional attributes for the message                 |

\*Either TopicArn or TargetArn must be provided.

#### Return Value

- **Type**: Object or Error Object
- **Description**: The response from SNS or an error object if the operation failed

#### Usage Examples

##### Publishing to a Topic

```javascript
const result = await publishNotificationToSNS({
  TopicArn: "arn:aws:sns:us-east-1:123456789012:MyTopic",
  Subject: "Order Confirmation",
  Message: "Your order #12345 has been confirmed and is being processed.",
});
// Sends the notification to all subscribers of the topic
```

##### Publishing with Message Attributes

```javascript
const result = await publishNotificationToSNS({
  TopicArn: "arn:aws:sns:us-east-1:123456789012:MyTopic",
  Subject: "System Alert",
  Message: "High CPU usage detected on server web-01",
  MessageAttributes: {
    severity: {
      DataType: "String",
      StringValue: "high",
    },
    timestamp: {
      DataType: "Number",
      StringValue: Date.now().toString(),
    },
  },
});
// Sends the notification with additional attributes
```

#### Error Handling

The function throws an error if neither TopicArn nor TargetArn is provided: `Either TopicArn or TargetArn should be passed`.

If there's an error publishing the notification, the function returns an error object:

```javascript
{
  statusCode: 500,
  body: JSON.stringify("Failed to send notification.")
}
```

If the endpoint is disabled, the function logs a debug message but doesn't throw an error.

#### Related Functions

- [sendMessageToSQSQueue](http://#sendmessagetosqsqueue)
- [addLogEntry](http://#addlogentry)

## S3 Operations

### getS3SignedUrl

#### Function Signature

```javascript
async getS3SignedUrl(bucket, key, expirySeconds = TWO_MINUTES_IN_SECONDS)
```

#### Description

Generates a pre-signed URL for accessing an object in an S3 bucket. This function creates a temporary URL that allows access to the specified S3 object without requiring AWS credentials, making it useful for providing temporary access to private objects.

#### Parameters

| Name          | Type   | Required | Default         | Description                                 |
| ------------- | ------ | -------- | --------------- | ------------------------------------------- |
| bucket        | string | Yes      | \-              | The name of the S3 bucket                   |
| key           | string | Yes      | \-              | The key (path) of the object in the bucket  |
| expirySeconds | number | No       | 120 (2 minutes) | The number of seconds until the URL expires |

#### Return Value

- **Type**: String
- **Description**: A pre-signed URL for accessing the S3 object

#### Usage Examples

##### Basic Example

```javascript
const url = await getS3SignedUrl("my-bucket", "path/to/file.pdf");
// Returns a URL that provides read access to the file for 2 minutes
```

##### Custom Expiry Time

```javascript
const url = await getS3SignedUrl(
  "my-bucket",
  "path/to/image.jpg",
  3600, // 1 hour
);
// Returns a URL that provides read access to the image for 1 hour
```

#### Error Handling

The function throws an error if either the bucket or key parameter is missing: `bucket is a required input` or `key is a required input`.

#### Related Functions

- [getS3UploadSignedUrl](http://#gets3uploadsignedurl)
- [getS3Object](http://#gets3object)
- [uploadToS3Bucket](http://#uploadtos3bucket)

### getS3UploadSignedUrl

#### Function Signature

```javascript
async getS3UploadSignedUrl({ bucket, key, expirySeconds = TWO_MINUTES_IN_SECONDS, options = {} }, isBase64Encoding = false)
```

#### Description

Generates a pre-signed URL for uploading an object to an S3 bucket. This function creates a temporary URL that allows clients to upload files directly to S3 without requiring AWS credentials, making it useful for secure client-side uploads.

#### Parameters

| Name                  | Type    | Required | Default         | Description                                       |
| --------------------- | ------- | -------- | --------------- | ------------------------------------------------- |
| options               | object  | Yes      | \-              | Options object                                    |
| options.bucket        | string  | Yes      | \-              | The name of the S3 bucket                         |
| options.key           | string  | Yes      | \-              | The key (path) for the object in the bucket       |
| options.expirySeconds | number  | No       | 120 (2 minutes) | The number of seconds until the URL expires       |
| options.options       | object  | No       | {}              | Additional options for the S3 PutObject operation |
| isBase64Encoding      | boolean | No       | false           | Whether the upload will be base64 encoded         |

#### Return Value

- **Type**: String
- **Description**: A pre-signed URL for uploading to the S3 bucket

#### Usage Examples

##### Basic Example

```javascript
const uploadUrl = await getS3UploadSignedUrl({
  bucket: "my-bucket",
  key: "uploads/user123/profile.jpg",
});
// Returns a URL that allows uploading a file to the specified path
```

##### With Custom Options and Expiry

```javascript
const uploadUrl = await getS3UploadSignedUrl({
  bucket: "my-bucket",
  key: "uploads/documents/contract.pdf",
  expirySeconds: 3600, // 1 hour
  options: {
    ContentType: "application/pdf",
    Metadata: {
      "user-id": "user123",
      "document-type": "contract",
    },
  },
});
// Returns a URL for uploading a PDF with specific content type and metadata
```

##### For Base64 Encoded Data

```javascript
const uploadUrl = await getS3UploadSignedUrl(
  {
    bucket: "my-bucket",
    key: "uploads/images/photo.jpg",
    options: {
      ContentType: "image/jpeg",
    },
  },
  true,
);
// Returns a URL for uploading a base64 encoded image
```

#### Error Handling

The function throws an error if either the bucket or key parameter is missing: `bucket is a required input` or `key is a required input`.

#### Related Functions

- [getS3SignedUrl](http://#gets3signedurl)
- [uploadToS3Bucket](http://#uploadtos3bucket)

### uploadToS3Bucket

#### Function Signature

```javascript
async uploadToS3Bucket({ bucket, key, body, options = {} }, isBase64Encoding = false)
```

#### Description

Uploads an object to an S3 bucket. This function provides a direct way to upload data to S3 from the server side, with support for various options and base64 encoded data.

#### Parameters

| Name             | Type             | Required | Default | Description                                       |
| ---------------- | ---------------- | -------- | ------- | ------------------------------------------------- |
| options          | object           | Yes      | \-      | Options object                                    |
| options.bucket   | string           | Yes      | \-      | The name of the S3 bucket                         |
| options.key      | string           | Yes      | \-      | The key (path) for the object in the bucket       |
| options.body     | string or Buffer | Yes      | \-      | The content to upload                             |
| options.options  | object           | No       | {}      | Additional options for the S3 PutObject operation |
| isBase64Encoding | boolean          | No       | false   | Whether the body is base64 encoded                |

#### Return Value

- **Type**: Object
- **Description**: The response from the S3 PutObject operation

#### Usage Examples

##### Basic Example

```javascript
const result = await uploadToS3Bucket({
  bucket: "my-bucket",
  key: "documents/report.txt",
  body: "This is the content of the report.",
});
// Uploads the text content to the specified path
```

##### Uploading JSON Data

```javascript
const data = {
  userId: "user123",
  preferences: {
    theme: "dark",
    notifications: true,
  },
};

const result = await uploadToS3Bucket({
  bucket: "my-bucket",
  key: "users/user123/preferences.json",
  body: JSON.stringify(data),
  options: {
    ContentType: "application/json",
  },
});
// Uploads the JSON data with the appropriate content type
```

##### Uploading Base64 Encoded Image

```javascript
const base64Image = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...";
const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, "");

const result = await uploadToS3Bucket(
  {
    bucket: "my-bucket",
    key: "images/photo.jpg",
    body: base64Data,
    options: {
      ContentType: "image/jpeg",
    },
  },
  true,
);
// Uploads the base64 encoded image
```

#### Error Handling

The function throws an error if any of the required parameters (bucket, key, body) are missing: `bucket is a required input`, `key is a required input`, or `body is a required input`.

#### Related Functions

- [getS3SignedUrl](http://#gets3signedurl)
- [getS3UploadSignedUrl](http://#gets3uploadsignedurl)
- [getS3Object](http://#gets3object)

### copyS3Object

#### Function Signature

```javascript
async copyS3Object({ bucket, key, copySource, options = {} })
```

#### Description

Copies an object within S3 or between S3 buckets. This function allows you to duplicate objects or move them to different locations without downloading and re-uploading the data.

#### Parameters

| Name               | Type   | Required | Default | Description                                           |
| ------------------ | ------ | -------- | ------- | ----------------------------------------------------- |
| options            | object | Yes      | \-      | Options object                                        |
| options.bucket     | string | Yes      | \-      | The name of the destination S3 bucket                 |
| options.key        | string | Yes      | \-      | The key (path) for the destination object             |
| options.copySource | string | Yes      | \-      | The source object to copy, in the format 'bucket/key' |
| options.options    | object | No       | {}      | Additional options for the S3 CopyObject operation    |

#### Return Value

- **Type**: Object
- **Description**: The response from the S3 CopyObject operation

#### Usage Examples

##### Basic Example

```javascript
const result = await copyS3Object({
  bucket: "destination-bucket",
  key: "path/to/destination.jpg",
  copySource: "source-bucket/path/to/source.jpg",
});
// Copies the image from the source to the destination
```

##### With Metadata Directive

```javascript
const result = await copyS3Object({
  bucket: "my-bucket",
  key: "processed/image.jpg",
  copySource: "my-bucket/uploads/image.jpg",
  options: {
    MetadataDirective: "REPLACE",
    Metadata: {
      processed: "true",
      "processor-version": "1.2.3",
    },
  },
});
// Copies the image within the same bucket and updates its metadata
```

#### Error Handling

The function throws an error if any of the required parameters (bucket, key, copySource) are missing: `bucket is a required input`, `key is a required input`, or `copySource is a required input`.

#### Related Functions

- [uploadToS3Bucket](http://#uploadtos3bucket)
- [getS3Object](http://#gets3object)

### getS3Object

#### Function Signature

```javascript
async getS3Object({ bucket, key, options = {} })
```

#### Description

Retrieves an object from an S3 bucket. This function provides direct access to S3 objects, returning the object's metadata and content.

#### Parameters

| Name            | Type   | Required | Default | Description                                       |
| --------------- | ------ | -------- | ------- | ------------------------------------------------- |
| options         | object | Yes      | \-      | Options object                                    |
| options.bucket  | string | Yes      | \-      | The name of the S3 bucket                         |
| options.key     | string | Yes      | \-      | The key (path) of the object to retrieve          |
| options.options | object | No       | {}      | Additional options for the S3 GetObject operation |

#### Return Value

- **Type**: Object
- **Description**: The response from the S3 GetObject operation, including the object's content and metadata

#### Usage Examples

##### Basic Example

```javascript
const result = await getS3Object({
  bucket: "my-bucket",
  key: "documents/report.pdf",
});
// Returns the PDF document with its metadata

// Access the content
const content = await result.Body.transformToString();
```

##### With Range Option

```javascript
const result = await getS3Object({
  bucket: "my-bucket",
  key: "videos/movie.mp4",
  options: {
    Range: "bytes=0-1048575", // First 1MB of the file
  },
});
// Returns just the first 1MB of the video file
```

#### Error Handling

The function throws an error if either the bucket or key parameter is missing: `bucket is a required input` or `key is a required input`.

#### Related Functions

- [getS3ObjectAsStream](http://#gets3objectasstream)
- [getS3SignedUrl](http://#gets3signedurl)
- [uploadToS3Bucket](http://#uploadtos3bucket)

### getS3ObjectAsStream

#### Function Signature

```javascript
async getS3ObjectAsStream({ bucket, key, options = {} })
```

#### Description

Retrieves an object from an S3 bucket as a readable stream. This function is particularly useful for processing large files without loading them entirely into memory, allowing for efficient streaming of data.

#### Parameters

| Name            | Type   | Required | Default | Description                                       |
| --------------- | ------ | -------- | ------- | ------------------------------------------------- |
| options         | object | Yes      | \-      | Options object                                    |
| options.bucket  | string | Yes      | \-      | The name of the S3 bucket                         |
| options.key     | string | Yes      | \-      | The key (path) of the object to retrieve          |
| options.options | object | No       | {}      | Additional options for the S3 GetObject operation |

#### Return Value

- **Type**: ReadableStream
- **Description**: A readable stream of the S3 object's content

#### Usage Examples

##### Streaming to a File

```javascript
const fs = require("fs");
const stream = await getS3ObjectAsStream({
  bucket: "my-bucket",
  key: "large-files/dataset.csv",
});

// Create a write stream to a local file
const fileStream = fs.createWriteStream("local-dataset.csv");

// Pipe the S3 stream to the file
stream.pipe(fileStream);

// Handle events
fileStream.on("finish", () => {
  console.log("File downloaded successfully");
});
```

##### Processing a Stream Line by Line

```javascript
const readline = require("readline");
const stream = await getS3ObjectAsStream({
  bucket: "my-bucket",
  key: "logs/application.log",
});

// Create a readline interface
const rl = readline.createInterface({
  input: stream,
  crlfDelay: Infinity,
});

// Process each line
let lineCount = 0;
rl.on("line", (line) => {
  lineCount++;
  // Process the line
  if (line.includes("ERROR")) {
    console.log(`Error found at line ${lineCount}: ${line}`);
  }
});

rl.on("close", () => {
  console.log(`Processed ${lineCount} lines`);
});
```

#### Error Handling

The function throws an error if either the bucket or key parameter is missing: `bucket is a required input` or `key is a required input`.

#### Related Functions

- [getS3Object](http://#gets3object)
- [getS3SignedUrl](http://#gets3signedurl)

## Verification Checklist

- ✅ All exported AWS service functions are documented
- ✅ All parameters are explained
- ✅ All return values are described
- ✅ Examples demonstrate proper usage
- ✅ No sections are incomplete or marked as "TODO"
