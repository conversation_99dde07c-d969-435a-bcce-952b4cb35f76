# Including Collections within Documents using Collection Sections

In frontm.ai, you can embed collections within documents using specialized sections. This creates a powerful UI pattern where tabular data is displayed within a form.

## Basic Pattern

1. Create a document to host the collection
2. Create a section with `forCollection: true`
3. Add the collection to the section using `addCollection()`

## Example from allInformation.js

```javascript
// 1. Create the host document
export const allInformationDoc = new Doc("allInformationDoc", state, {
  title: "All Information",
  description: "Comprehensive information display",
  autoSave: false,
});

// 2. Create a section with forCollection: true
export const allInformationSection = new Section("allInformationSection", {
  title: "Information Overview",
  collapsable: false,
  columns: 1,
  doc: allInformationDoc,
  forCollection: true, // This is key for collection sections
  state,
});

// 3. Create your collection normally
export const serviceProviderSummaryCollection = new Collection(
  "serviceProviderSummary",
  {
    title: "Service Provider Summary",
    document: serviceProviderSummaryDoc,
    // other collection properties...
  },
);

// 4. Add the collection to the section
allInformationSection.addCollection(serviceProviderSummaryCollection);
```

This pattern creates a document with an embedded collection, allowing users to view and interact with tabular data within the context of a form. The subcollection can be part of the main doc, or it could be unrelated. If it is unrelated, you will beed to load the data in the collection before you send the form response. This pattern also works inside of containers, so sending a container response will also build the data within the form as expected.
