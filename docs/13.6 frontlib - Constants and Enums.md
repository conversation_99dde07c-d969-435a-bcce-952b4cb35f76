# Constants and Enums

## Table of Contents

- System Constants
  - SYSTEM_DB
  - PERFORMANCE_THRESHOLD
  - Time Constants
- Table and Collection Names
  - PEOPLE_TABLE
  - COLLECTION_BOT_FARM
  - DOMAINS_TABLE
  - KEY_VALUES_TABLE
  - ACTIVITY_TABLE
  - MESSAGES_COLLECTION
- Field Definitions
  - PEOPLE_SECURED_FIELDS
  - PEOPLE_CONTACT_INFO_FIELDS
  - MESSAGES_SECURED_FIELDS
  - ACTIVITY_FIELDS
  - TTL_FIELD
- Fetch Direction
  - FETCH_DIRECTION
- MongoDB Constants
  - MONGO_MANAGER
  - MONGO_ACTIONS
  - MONGO_ERROR_CODES
- ElasticSearch Constants
  - ELASTI_SEARCH
  - USER_LAST_ACTIVITY_ES_INDEX
  - EVENT_NAMES_ACTION_MAP
  - TABLE_META
- Lambda Constants
  - LAMBDA_INVOCATION_TYPES
- Queue Constants
  - LOGS_QUEUE and LOGS_GROUP
  - ACTIVITY_QUEUE and ACTIVITY_GROUP
  - BUSINESS_DATA_HANDLER_QUEUE and BUSINESS_DATA_GROUP

## System Constants

### SYSTEM_DB

```javascript
export const SYSTEM_DB = "frontmai";
```

#### Description

The name of the main system database. This constant is used throughout the library when interacting with the database to ensure consistency.

#### Usage Example

```javascript
const params = {
  action: MONGO_ACTIONS.QUERY,
  db: SYSTEM_DB,
  collection: PEOPLE_TABLE,
  query: { userId: "user123" },
};
```

### PERFORMANCE_THRESHOLD

```javascript
const PERFORMANCE_THRESHOLD = 1000;
```

#### Description

The threshold in milliseconds for performance logging. Operations that take longer than this threshold will have their detailed data included in performance logs, while faster operations will have a more concise log entry.

#### Usage Example

```javascript
// In addPerfLogEntry function
if (more.duration > PERFORMANCE_THRESHOLD) {
  // Include detailed data in the log
} else {
  // Use a more concise log format
}
```

### Time Constants

```javascript
const TWO_MINUTES_IN_SECONDS = 2 * 60;
const ONE_WEEK_SECONDS = 7 * 24 * 60 * 60;
const ONE_DAY_AGO = Date.now() - 24 * 60 * 60 * 1000;
```

#### Description

Constants representing common time periods used throughout the library:

- `TWO_MINUTES_IN_SECONDS`: 120 seconds, used as the default expiry for S3 signed URLs
- `ONE_WEEK_SECONDS`: 604,800 seconds (7 days), used for cache expiration
- `ONE_DAY_AGO`: Timestamp from 24 hours ago, used for activity queries

#### Usage Example

```javascript
// Getting a signed URL with the default expiry
const url = await getS3SignedUrl("my-bucket", "path/to/file.pdf");

// Setting cache expiry
const cacheEntry = {
  key: `People_${userId}`,
  value: JSON.stringify(userObj),
  expiry: ONE_WEEK_SECONDS,
};

// Getting active users from the last day
const activeUsers = await getActiveUsersList({
  startTime: ONE_DAY_AGO,
  domain: "example.com",
});
```

## Table and Collection Names

### PEOPLE_TABLE

```javascript
const PEOPLE_TABLE = "People";
```

#### Description

The name of the table/collection that stores user information. This constant is used when performing operations on user data.

#### Usage Example

```javascript
const user = await dbGetWithKey(PEOPLE_TABLE, { userId: "user123" });
```

### COLLECTION_BOT_FARM

```javascript
const COLLECTION_BOT_FARM = "BotFarm";
```

#### Description

The name of the collection that stores bot configurations and data. This constant is used when retrieving or updating bot information.

#### Usage Example

```javascript
const bot = await dbGetWithIndex(COLLECTION_BOT_FARM, null, "botId = :botId", {
  ":botId": botId,
});
```

### DOMAINS_TABLE

```javascript
const DOMAINS_TABLE = "Domains";
```

#### Description

The name of the table that stores domain information. This constant is used when retrieving or updating domain data.

#### Usage Example

```javascript
const domain = await dbGetWithKey(DOMAINS_TABLE, { userDomain: domainId });
```

### KEY_VALUES_TABLE

```javascript
const KEY_VALUES_TABLE = "_keyValues";
```

#### Description

The name of the table that stores key-value pairs for system configuration and static data. This constant is used when retrieving system settings or reference data.

#### Usage Example

```javascript
const config = await getSystemStaticData("appConfig");
```

### ACTIVITY_TABLE

```javascript
const ACTIVITY_TABLE = "_activity";
```

#### Description

The name of the table that stores user activity records. This constant is used when querying or logging user activities.

#### Usage Example

```javascript
const query = {
  timestamp: { $gt: startTime, $lt: endTime },
  domain,
};

const params = {
  action: MONGO_ACTIONS.QUERY,
  db: SYSTEM_DB,
  collection: ACTIVITY_TABLE,
  query,
};
```

### MESSAGES_COLLECTION

```javascript
const MESSAGES_COLLECTION = "Messages";
```

#### Description

The name of the collection that stores message data. This constant is used when retrieving or inserting messages.

#### Usage Example

```javascript
const messages = await getMessages("conv123");
```

## Field Definitions

### PEOPLE_SECURED_FIELDS

```javascript
const PEOPLE_SECURED_FIELDS = [
  "emailAddress",
  "phoneNumbers.land",
  "phoneNumbers.satellite",
  "phoneNumbers.mobile",
  "address.city",
  "address.addressLine1",
  "address.postCode",
];
```

#### Description

An array of field names in the People collection that contain sensitive information and should be encrypted. These fields are automatically handled by the database operations to ensure data security.

#### Usage Example

```javascript
let params = {
  action: MONGO_ACTIONS.INSERT_ONE,
  db: SYSTEM_DB,
  collection: PEOPLE_TABLE,
  document: dbUser,
  securedFields: PEOPLE_SECURED_FIELDS,
  adminAccess: true,
};
```

### PEOPLE_CONTACT_INFO_FIELDS

```javascript
const PEOPLE_CONTACT_INFO_FIELDS = [
  "userName",
  "userCompanyName",
  "domainsSearch",
  "address",
  "phoneNumbers",
  "nationality",
  "sailingStatus",
  "shipName",
  "shipIMO",
  "rank",
  "role",
  "rankLevel1",
  "rankLevel2",
  "rankLevel3",
  "media",
  "mentor",
  "aboutMe",
  "searchable",
  "visible",
  "userTimezone",
];
```

#### Description

An array of field names in the People collection that represent contact information. These fields are used to determine when to update a user's contact information cache.

#### Usage Example

```javascript
// In updateUser function
let updateContactInfo = !_.isEmpty(
  _.pick(document, PEOPLE_CONTACT_INFO_FIELDS),
);
if (updateContactInfo) {
  await updateUserCache(users[0], true);
}
```

### MESSAGES_SECURED_FIELDS

```javascript
const MESSAGES_SECURED_FIELDS = ["content"];
```

#### Description

An array of field names in the Messages collection that contain sensitive information and should be encrypted. These fields are automatically handled by the database operations to ensure data security.

#### Usage Example

```javascript
let params = {
  action: MONGO_ACTIONS.INERT_ARRAY,
  db: SYSTEM_DB,
  collection: MESSAGES_COLLECTION,
  documents: messages,
  securedFields: MESSAGES_SECURED_FIELDS,
  dateFields: [TTL_FIELD],
  adminAccess: true,
};
```

### ACTIVITY_FIELDS

```javascript
const ACTIVITY_FIELDS = [
  "messageId",
  "appVersion",
  "botId",
  "botName",
  "client",
  "domain",
  "domainName",
  "location",
  "messageFromUserMetadata",
  "brand",
  "osVersion",
  "phoneModel",
  "platform",
  "stamp",
  "timestamp",
  "userAgent",
  "userEmail",
  "userId",
  "responsesArray",
];
```

#### Description

An array of field names that are relevant for activity tracking. These fields are used to filter activity objects before logging them to ensure only relevant data is stored.

#### Usage Example

```javascript
// In logActivity function
const filteredActivity = _.pick(activity, ACTIVITY_FIELDS);
if (!_.isEmpty(filteredActivity)) {
  return sendMessageToSQSQueue(
    filteredActivity,
    ACTIVITY_QUEUE,
    ACTIVITY_GROUP,
  );
}
```

### TTL_FIELD

```javascript
const TTL_FIELD = "ttl";
```

#### Description

The name of the field used for time-to-live (TTL) values in database documents. This field is used to automatically expire documents after a certain time.

#### Usage Example

```javascript
// In createUserWithTtl function
dbUser.ttl = ttl;
return createUser(dbUser, ["ttl"]);
```

## Fetch Direction

### FETCH_DIRECTION

```javascript
const FETCH_DIRECTION = {
  NEWER: "NEWER",
  OLDER: "OLDER",
};
```

#### Description

An enumeration of possible directions for fetching messages. This is used to control whether to retrieve messages newer or older than a specified timestamp.

#### Usage Example

```javascript
// Get older messages
const olderMessages = await getMessages(
  "conv123",
  lastMessageTimestamp,
  20,
  -1,
  FETCH_DIRECTION.OLDER,
);

// Get newer messages
const newerMessages = await getMessages(
  "conv123",
  lastMessageTimestamp,
  20,
  1,
  FETCH_DIRECTION.NEWER,
);
```

## MongoDB Constants

### MONGO_MANAGER

```javascript
export const MONGO_MANAGER = "MongoDBManager";
```

#### Description

The name of the Lambda function that handles MongoDB operations. This constant is used when invoking the MongoDB manager Lambda.

#### Usage Example

```javascript
const response = await invokeLambda(MONGO_MANAGER, "RequestResponse", params);
```

### MONGO_ACTIONS

```javascript
export const MONGO_ACTIONS = {
  INSERT_ONE: "insertOne",
  INERT_ARRAY: "insertArray",
  UPDATE: "update",
  QUERY: "query",
  DELETE: "delete",
  BULK_WRITE: "bulkWrite",
};
```

#### Description

An enumeration of possible MongoDB actions that can be performed by the MongoDB manager. These values are used to specify the operation type when invoking the MongoDB manager Lambda.

#### Usage Example

```javascript
const params = {
  action: MONGO_ACTIONS.INSERT_ONE,
  db: SYSTEM_DB,
  collection: PEOPLE_TABLE,
  document: dbUser,
  securedFields: PEOPLE_SECURED_FIELDS,
  adminAccess: true,
  dateFields,
};

const response = await invokeLambda(MONGO_MANAGER, "RequestResponse", params);
```

### MONGO_ERROR_CODES

```javascript
const MONGO_ERROR_CODES = {
  DUPLICATED_DOCUMENTS: 11000,
};
```

#### Description

An enumeration of MongoDB error codes that can be returned by the database. These codes are used to identify specific error conditions and handle them appropriately.

#### Usage Example

```javascript
// In insertMessages function
if (_.get(results, "body.code") === MONGO_ERROR_CODES.DUPLICATED_DOCUMENTS) {
  errMsg = "Duplicated messages found";
}
```

## ElasticSearch Constants

### ELASTI_SEARCH

```javascript
const ELASTI_SEARCH = "elastiSearch";
```

#### Description

The name of the Lambda function that handles Elasticsearch operations. This constant is used when invoking the Elasticsearch Lambda.

#### Usage Example

```javascript
const params = {
  method: "POST",
  docType: "_doc",
  doc,
  index,
};
return invokeLambda(ELASTI_SEARCH, "Event", params);
```

### USER_LAST_ACTIVITY_ES_INDEX

```javascript
const USER_LAST_ACTIVITY_ES_INDEX = "audit_frontmai__activity";
```

#### Description

The name of the Elasticsearch index that stores user activity data. This constant is used when indexing user activity records.

#### Usage Example

```javascript
// In updateUserLastActivity function
await addDocToESIndex(esDoc, USER_LAST_ACTIVITY_ES_INDEX);
```

### EVENT_NAMES_ACTION_MAP

```javascript
const EVENT_NAMES_ACTION_MAP = {
  INSERT: { method: "PUT", eventName: "INSERT" },
  MODIFY: { method: "PUT", eventName: "MODIFY" },
  REMOVE: { method: "DELETE", eventName: "REMOVE" },
};
```

#### Description

A mapping of event names to their corresponding HTTP methods and event names for Elasticsearch operations. This constant is used to determine the appropriate method when writing to Elasticsearch.

#### Usage Example

```javascript
// In writeToESIndex function
let eventMap = EVENT_NAMES_ACTION_MAP[eventName];
let params = {
  eventName,
  method: eventMap.method,
  index: tableMetaData.index,
  docType: tableMetaData.docType,
  id: doc[tableMetaData.Keys],
  doc: doc,
  originalDocument,
};
```

### TABLE_META

```javascript
const TABLE_META = {
  People: { index: "people_index", Keys: "userId", docType: "_doc" },
};
```

#### Description

Metadata for database tables, including their corresponding Elasticsearch index, key field, and document type. This constant is used when writing to Elasticsearch to ensure the correct mapping.

#### Usage Example

```javascript
// In createUser function
await writeToESIndex("INSERT", TABLE_META.People, dbUser);
```

## Lambda Constants

### LAMBDA_INVOCATION_TYPES

```javascript
export const LAMBDA_INVOCATION_TYPES = {
  EVENT: "Event",
  REQUEST_RESPONSE: "RequestResponse",
};
```

#### Description

An enumeration of possible Lambda invocation types. These values are used to specify whether to invoke a Lambda function synchronously or asynchronously.

#### Usage Example

```javascript
// Synchronous invocation
const result = await invokeLambda(
  "ProcessOrder",
  LAMBDA_INVOCATION_TYPES.REQUEST_RESPONSE,
  { orderId: "12345" },
);

// Asynchronous invocation
await invokeLambda("SendNotification", LAMBDA_INVOCATION_TYPES.EVENT, {
  userId: "user123",
  message: "Hello!",
});
```

## Queue Constants

### LOGS_QUEUE and LOGS_GROUP

```javascript
export const LOGS_QUEUE = process.env.LOGS_QUEUE;
export const LOGS_GROUP = "logs";
```

#### Description

Constants for the SQS queue and message group used for system logs. These constants are used when sending log messages to the logging system.

#### Usage Example

```javascript
// In addLogEntry function
return sendMessageToSQSQueue(params, LOGS_QUEUE, LOGS_GROUP);
```

### ACTIVITY_QUEUE and ACTIVITY_GROUP

```javascript
export const ACTIVITY_QUEUE = process.env.ACTIVITY_QUEUE;
export const ACTIVITY_GROUP = "activities";
```

#### Description

Constants for the SQS queue and message group used for activity tracking. These constants are used when logging user activities.

#### Usage Example

```javascript
// In logActivity function
return sendMessageToSQSQueue(filteredActivity, ACTIVITY_QUEUE, ACTIVITY_GROUP);
```

### BUSINESS_DATA_HANDLER_QUEUE and BUSINESS_DATA_GROUP

```javascript
export const BUSINESS_DATA_HANDLER_QUEUE =
  process.env.BUSINESS_DATA_HANDLER_QUEUE;
export const BUSINESS_DATA_GROUP = "BusinessData";
```

#### Description

Constants for the SQS queue and message group used for business data handling. These constants are used when sending business data messages.

#### Usage Example

```javascript
// Example usage in a business data processing function
return sendMessageToSQSQueue(
  businessData,
  BUSINESS_DATA_HANDLER_QUEUE,
  BUSINESS_DATA_GROUP,
);
```

## Verification Checklist

- ✅ All exported constants and enums are documented
- ✅ All values are explained
- ✅ Usage examples demonstrate proper usage
- ✅ No sections are incomplete or marked as "TODO"
