# Maps

Maps are collections of style MAP that implement an object called “mapOptions” passed to the collection constructor.

## Core Map Configuration

- **region**: Defines the initial map view coordinates and zoom level

  - **latitude**: Floating point value (1.1) representing the center latitude in decimal degrees

  - **longitude**: Floating point value (1.1) representing the center longitude in decimal degrees

  - **zoom**: Integer value (2) defining the initial zoom level of the map, with higher values indicating closer zoom

- **type**: String value ("streetSat") specifying the map rendering style, likely a hybrid of street map and satellite imagery

- **geoJsonUrl**: Object containing URLs to GeoJSON data files for different map overlays

  - **FAO**: URL string pointing to Food and Agriculture Organisation boundaries data

  - **EEZ**: URL string pointing to Exclusive Economic Zone boundaries data

- **drawer**: Boolean value (true) controlling the visibility of a drawer/sidebar UI component

- **table**: Boolean value (true) enabling tabular data display as an alternative to the map

- **editableOverlay**: What is this?

- **enableVisualElements**: What is this?

- **weatherOverlay**: Boolean value (true) enabling weather data overlay on the map

- **weatherOverlayOptions**: Object controlling specific weather visualisation features

  - **cloud**: Boolean value (true) enabling cloud coverage visualisation

  - **windSpeed**: Boolean value (true) enabling wind speed visualisation

- **imperial**: Boolean value (false) determining whether to use imperial units (false means metric units)

- **descriptionText**: String value ("Location") likely used as a label or header for location information

- **routeFields**: Empty array that contain fields related to the routes to be displayed in the map

```json
"mapOptions": {
  "region": {
     "latitude": 1.1,
     "longitude": 1.1,
     "zoom": 2
   },
   "geoJsonUrl": {
      "FAO": "https://devproxy.frontm.ai/botLogos/testFiles/thuraya_FAO.json",
      "EEZ": "https://frontm-contentdelivery-mobilehub-1030065648.s3.amazonaws.com/botLogos/testFiles/thuraya_eez.json"
   },
   "type": "streetSat",
   "drawer": true,
   "table": true,
   "editableOverlay": false,
   "weatherOverlay": true,
   "weatherOverlayOptions": {
     "cloud": true,
     "windSpeed": true
   },
   "enableVisualElements": true,
   "imperial": false,
   "descriptionText": "Location",
   "routeFields": [
      {
         "routeId": "reouteId",
         "routeColour": "#HTML colour",
         "routeWidth": 2,
         "routeType": 1,
      }
   ]
}

```
