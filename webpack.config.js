const path = require("path");

const appDirectory = path.resolve(__dirname);
const BASE_FRAMEWORK_PATH = "./node_modules/@frontmltd/frontmjs";
const FRAMEWORK_DIR = path.resolve(appDirectory, BASE_FRAMEWORK_PATH);

const babelLoaderConfiguration = {
  test: /\.js$/,
  // Add every directory that needs to be compiled by Babel during the build.
  include: [FRAMEWORK_DIR, path.resolve(appDirectory, "src")],
  use: {
    loader: "babel-loader",
    options: {
      presets: [["@babel/preset-env", { forceAllTransforms: true }]],
      env: {},
    },
  },
};

const config = {
  entry: [BASE_FRAMEWORK_PATH],
  output: {
    path: path.resolve(__dirname, "dist"),
    environment: {
      arrowFunction: false,
    },
    library: {
      name: "botModule",
      type: "assign",
    },
  },
  module: {
    rules: [babelLoaderConfiguration],
  },
  resolve: {
    modules: [
      path.resolve(__dirname + "/src"),
      path.resolve(__dirname + "/node_modules"),
    ],
  },
};

module.exports = () => {
  config.mode = "production";
  config.target = ["node", "es5"];
  return config;
};
