{"version": "1.0.0", "name": "emma", "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@webpack-cli/generators": "^3.0.7", "babel-loader": "^10.0.0", "eslint": "^8.57.1", "prettier": "^3.5.3", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}, "bin": {"frontm-build": "./node_modules/@frontmltd/frontmjs/after.js"}, "scripts": {"build": "npm run format && webpack --mode=production --node-env=production && npm exec frontm-build", "build:dev": "webpack --mode=development && npm exec frontm-build", "build:prod": "webpack --mode=production --node-env=production && npm exec frontm-build", "format": "prettier --write .", "watch": "webpack --watch"}, "dependencies": {"@frontmltd/frontmjs": "^4.4.26"}}