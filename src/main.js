import { D, state } from "@frontmltd/frontmjs/core/State";
import { Intent } from "@frontmltd/frontmjs/core/Intent";
import { SYSTEM_INTENTS } from "@frontmltd/frontmjs/core/ALLConstants";
import { Field } from "@frontmltd/frontmjs/core/Field";
import { FormFieldTypes } from "@frontmltd/frontmjs/core/FormFieldTypes";
import { Doc } from "@frontmltd/frontmjs/core/Doc";

export async function invokeLambda(functionName, invocationType, jsonPayload) {
  try {
    D.log({ message: `Invoking Lambda: ${functionName}` });
    return await state.callLambda(functionName, jsonPayload, invocationType);
  } catch (error) {
    console.error(
      `Error calling lambda ${functionName} with error object ${error}`,
    );
  }
}

export let main = new Intent(SYSTEM_INTENTS.MAIN, state);
main.runOnCloud();

main.onMatching = () => {
  D.log({ message: "Main intent matched", data: state.messageFromUser });
  state.messageFromUser.intentId === SYSTEM_INTENTS.MAIN;
};

main.onResolution = async () => {
  D.log({ message: "Main intent resolved" });

  "Welcome to the TV Guide Bot! You can ask me about TV listings by typing something like 'What's on TravelXP for the next 3 hours?'".sendResponse();
  "Supported channels: TravelXP, ProTV, Fast&Fun Box, Fight Box, TV5, A2Z, Film Box".sendResponse();
};

// Create an intent to handle TV listing requests
export let tvListingIntent = new Intent("tvListingIntent", state);
tvListingIntent.runOnCloud(); // Run this intent on cloud

// Global variable to store the user message
let capturedUserMessage = null;

tvListingIntent.onMatching = () => {
  D.log({
    message: "Matching phase - checking for user message",
    data: state.messageFromUser,
  });

  // Try to capture the message during matching when it might be available
  if (state.messageFromUser?.content) {
    capturedUserMessage = state.messageFromUser.content;
    D.log({
      message: "Captured message during matching",
      content: capturedUserMessage,
    });
  }

  return state.messageFromUser?.intentId !== SYSTEM_INTENTS.MAIN;
};

tvListingIntent.onResolution = async (params) => {
  // Let the user know we're processing their request
  "Looking up TV listings for you...".sendResponse();
  try {
    // Wait for user input to be fully processed
    let userQuery = null;

    // First check if we captured the message during matching
    if (capturedUserMessage) {
      userQuery = capturedUserMessage;
      D.log({
        message: "Using message captured during matching",
        query: userQuery,
      });
    } else {
      D.log({
        message: "No message captured during matching, trying other methods",
      });

      let attempts = 0;
      const maxAttempts = 5; // Reduce attempts since we're trying matching first

      // Try multiple times with increasing delays to capture the user message
      while (!userQuery && attempts < maxAttempts) {
        attempts++;

        // Wait progressively longer each attempt
        const delay = attempts * 100; // 100ms, 200ms, 300ms, etc.
        await new Promise((resolve) => setTimeout(resolve, delay));

        D.log({
          message: `Attempt ${attempts} to capture user message`,
          messageFromUser: state.messageFromUser,
          content: state.messageFromUser?.content,
        });

        // Try to get the user's actual message
        if (state.messageFromUser?.content) {
          userQuery = state.messageFromUser.content;
          D.log({
            message: "Successfully captured user message",
            query: userQuery,
          });
          break;
        }

        // Also try to get from conversation history
        try {
          const recentMessages = await frontmlib.getMessages(
            state.conversationId,
            Date.now(),
            3,
          );
          if (recentMessages && recentMessages.length > 0) {
            // Find the most recent user message (not from bot)
            const latestUserMessage = recentMessages.find(
              (msg) =>
                msg.content &&
                msg.createdBy !== "bot" &&
                typeof msg.content === "string" &&
                msg.content.trim().length > 0,
            );

            if (latestUserMessage) {
              userQuery = latestUserMessage.content;
              D.log({
                message: "Captured user message from conversation history",
                query: userQuery,
                attempt: attempts,
              });
              break;
            }
          }
        } catch (error) {
          D.log({
            message: "Error accessing conversation history",
            error: error.message,
          });
        }
      }

      // Close the else block for when no message was captured during matching
    }

    // Fallback if we still couldn't capture the message
    if (!userQuery) {
      userQuery = "What's on ProTV for the next 7 hours?";
      D.log({
        message: "Using fallback query after all attempts",
        query: userQuery,
      });
    }

    D.log({ message: "Final user query", query: userQuery });

    D.log({ message: "Calling Lambda with query", data: userQuery });

    // Call the AWS Lambda function using frontmlib
    const lambdaResult = await invokeLambda(
      "tvScheduleFetcher",
      "RequestResponse",
      {
        queryStringParameters: {
          q: userQuery,
        },
      },
    );

    D.log({ message: "Lambda result", data: lambdaResult });

    // Parse the complex nested JSON response from Lambda
    let listings;
    try {
      D.log({ message: "Raw Lambda result", data: lambdaResult });

      // Step 1: Check if we have a Payload property (AWS Lambda response format)
      let responseData = lambdaResult;
      if (lambdaResult.Payload) {
        // Parse the Payload string
        responseData = JSON.parse(lambdaResult.Payload);
        D.log({ message: "Parsed Payload", data: responseData });
      }

      // Step 2: Check if the response has the expected structure
      if (!responseData || responseData.statusCode !== 200) {
        const errorMessage = responseData?.body || "No results found";
        D.log({ message: "Invalid Lambda response", data: errorMessage });
        `Sorry, I couldn't find any TV listings. ${errorMessage}`.sendResponse();
        return;
      }

      // Step 3: Parse the body if it's a string
      let bodyData = responseData;
      if (responseData.body && typeof responseData.body === "string") {
        bodyData = JSON.parse(responseData.body);
        D.log({ message: "Parsed body", data: bodyData });
      } else if (responseData.body) {
        bodyData = responseData.body;
      }

      // Step 4: Get the final result
      listings = bodyData;

      // Step 5: If the result contains escaped newlines, clean them up for display
      if (listings.result && typeof listings.result === "string") {
        listings.result = listings.result.replace(/\\n/g, "\n");
        D.log({ message: "Cleaned up result", data: listings.result });
      }
    } catch (e) {
      D.log({
        message: "Error parsing Lambda result",
        error: e.message,
        stack: e.stack,
      });
      "Sorry, I encountered an error processing the TV listings.".sendResponse();
      return;
    }

    D.log({ message: "Parsed listings", listings: listings });

    // Display the TV listings
    if (listings.result) {
      // Split the result by newlines and format each listing
      const listings_lines = listings.result
        .split("\n")
        .filter((line) => line.trim()) // Remove empty lines
        .map((line) => line.trim()); // Clean up whitespace

      D.log({ message: "Formatted listings lines", data: listings_lines });

      // Create a single formatted message with HTML line breaks
      const numberedListings = listings_lines.map(
        (line, index) => `${index + 1}. ${line}`,
      );
      const htmlFormattedMessage = `Here are the TV listings:<br><br>${numberedListings.join("<br>")}`;

      // Send as a single HTML-formatted message
      htmlFormattedMessage.sendResponse();
    } else {
      "No TV listings found for this time period.".sendResponse();
    }
  } catch (error) {
    D.log({ message: "Error fetching TV listings", data: error.message });
    "Sorry, I encountered an error while fetching TV listings. Please try again later.".sendResponse();
  }
};

// Create a fallback intent for messages that don't match other intents
export let fallbackIntent = new Intent("fallbackIntent", state);

fallbackIntent.runOnCloud(); // Run this intent on cloud

fallbackIntent.onMatching = () =>
  state.messageFromUser.intentId === "fallbackIntent";

fallbackIntent.onResolution = async () => {
  D.log({ message: "Fallback intent resolved" });
  "I can show you TV listings for specific channels. Try asking something like 'What's on TravelXP for the next 3 hours?'".sendResponse();
  "Supported channels: TravelXP, ProTV, Fast&Fun Box, Fight Box, TV5, A2Z, Film Box".sendResponse();
};
