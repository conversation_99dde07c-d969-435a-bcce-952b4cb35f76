import { D, state } from "@frontmltd/frontmjs/core/State";
import { Intent } from "@frontmltd/frontmjs/core/Intent";
import { SYSTEM_INTENTS } from "@frontmltd/frontmjs/core/ALLConstants";
import { Field } from "@frontmltd/frontmjs/core/Field";
import { FormFieldTypes } from "@frontmltd/frontmjs/core/FormFieldTypes";
import { Doc } from "@frontmltd/frontmjs/core/Doc";

export async function invokeLambda(functionName, invocationType, jsonPayload) {
  try {
    D.log({ message: `Invoking Lambda: ${functionName}` });
    return await state.callLambda(functionName, jsonPayload, invocationType);
  } catch (error) {
    console.error(
      `Error calling lambda ${functionName} with error object ${error}`,
    );
  }
}

export let main = new Intent(SYSTEM_INTENTS.MAIN, state);
main.runOnCloud();

main.onMatching = () => {
  D.log({ message: "Main intent matched", data: state.messageFromUser });
  state.messageFromUser.intentId === SYSTEM_INTENTS.MAIN;
};

main.onResolution = async () => {
  D.log({ message: "Main intent resolved" });

  "Welcome to the TV Guide Bot! You can ask me about TV listings by typing something like 'What's on TravelXP for the next 3 hours?'".sendResponse();
  "Supported channels: TravelXP, ProTV, Fast&Fun Box, Fight Box, TV5, A2Z, Film Box".sendResponse();
};

// Create an intent to handle TV listing requests
export let tvListingIntent = new Intent("tvListingIntent", state);
tvListingIntent.runOnCloud(); // Run this intent on cloud

tvListingIntent.onMatching = () => {
  D.log({
    message: "Matching phase - checking for user message",
    data: state.messageFromUser,
  });

  // Match any message that's not the main intent
  return state.messageFromUser?.intentId !== SYSTEM_INTENTS.MAIN;
};

tvListingIntent.onResolution = async () => {
  // Let the user know we're processing their request
  "Looking up TV listings for you...".sendResponse();
  try {
    // Use the direct approach from FrontM documentation
    const userQuery = state.messageFromUser;

    D.log({
      message: "Direct access to user message",
      userQuery: userQuery,
      messageFromUser: state.messageFromUser,
    });

    // If we don't have a query, use fallback
    const finalQuery = userQuery || "What's on ProTV for the next 7 hours?";

    D.log({
      message: "Final user query",
      query: finalQuery,
    });

    D.log({ message: "Calling Lambda with query", data: finalQuery });

    // Call the AWS Lambda function using frontmlib
    const lambdaResult = await invokeLambda(
      "tvScheduleFetcher",
      "RequestResponse",
      {
        queryStringParameters: {
          q: finalQuery,
        },
      },
    );

    D.log({ message: "Lambda result", data: lambdaResult });

    // Parse the complex nested JSON response from Lambda
    let listings;
    try {
      D.log({ message: "Raw Lambda result", data: lambdaResult });

      // Step 1: Check if we have a Payload property (AWS Lambda response format)
      let responseData = lambdaResult;
      if (lambdaResult.Payload) {
        // Parse the Payload string
        responseData = JSON.parse(lambdaResult.Payload);
        D.log({ message: "Parsed Payload", data: responseData });
      }

      // Step 2: Check if the response has the expected structure
      if (!responseData || responseData.statusCode !== 200) {
        const errorMessage = responseData?.body || "No results found";
        D.log({ message: "Invalid Lambda response", data: errorMessage });
        `Sorry, I couldn't find any TV listings. ${errorMessage}`.sendResponse();
        return;
      }

      // Step 3: Parse the body if it's a string
      let bodyData = responseData;
      if (responseData.body && typeof responseData.body === "string") {
        bodyData = JSON.parse(responseData.body);
        D.log({ message: "Parsed body", data: bodyData });
      } else if (responseData.body) {
        bodyData = responseData.body;
      }

      // Step 4: Get the final result
      listings = bodyData;

      // Step 5: If the result contains escaped newlines, clean them up for display
      if (listings.result && typeof listings.result === "string") {
        listings.result = listings.result.replace(/\\n/g, "\n");
        D.log({ message: "Cleaned up result", data: listings.result });
      }
    } catch (e) {
      D.log({
        message: "Error parsing Lambda result",
        error: e.message,
        stack: e.stack,
      });
      "Sorry, I encountered an error processing the TV listings.".sendResponse();
      return;
    }

    D.log({ message: "Parsed listings", listings: listings });

    // Display the TV listings
    if (listings.result) {
      // Split the result by newlines and format each listing
      const listings_lines = listings.result
        .split("\n")
        .filter((line) => line.trim()) // Remove empty lines
        .map((line) => line.trim()); // Clean up whitespace

      D.log({ message: "Formatted listings lines", data: listings_lines });

      // Create a single formatted message with HTML line breaks
      const numberedListings = listings_lines.map(
        (line, index) => `${index + 1}. ${line}`,
      );
      const htmlFormattedMessage = `Here are the TV listings:<br><br>${numberedListings.join("<br>")}`;

      // Send as a single HTML-formatted message
      htmlFormattedMessage.sendResponse();
    } else {
      "No TV listings found for this time period.".sendResponse();
    }
  } catch (error) {
    D.log({ message: "Error fetching TV listings", data: error.message });
    "Sorry, I encountered an error while fetching TV listings. Please try again later.".sendResponse();
  }
};
