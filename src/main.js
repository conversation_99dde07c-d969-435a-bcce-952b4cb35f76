import { D, state } from "@frontmltd/frontmjs/core/State";
import { Intent } from "@frontmltd/frontmjs/core/Intent";
import { SYSTEM_INTENTS } from "@frontmltd/frontmjs/core/ALLConstants";
import { Field } from "@frontmltd/frontmjs/core/Field";
import { FormFieldTypes } from "@frontmltd/frontmjs/core/FormFieldTypes";
import { Doc } from "@frontmltd/frontmjs/core/Doc";

export async function invokeLambda(functionName, invocationType, jsonPayload) {
  try {
    D.log({ message: `Invoking Lambda: ${functionName}` });
    return await state.callLambda(functionName, jsonPayload, invocationType);
    // return await frontmlib.invokeLambda(
    //   functionName, // Replace with your actual Lambda function name
    //   invocationType,
    //   jsonPayload
    // );
  } catch (error) {
    console.error(
      `Error calling lambda ${functionName} with error object ${error}`,
    );
  }
}

// Create the main intent for initial greeting
export let main = new Intent(SYSTEM_INTENTS.MAIN, state);

main.runOnCloud();

// main.onMatching = () =>
//   state.messageFromUser.intentId === SYSTEM_INTENTS.MAIN;

main.onResolution = async () => {
  D.log({ message: "Main intent resolved" });

  "Welcome to the TV Guide Bot! You can ask me about TV listings by typing something like 'What's on TravelXP for the next 3 hours?'".sendResponse();
  "Supported channels: TravelXP, ProTV, Fast&Fun Box, Fight Box, TV5, A2Z, Film Box".sendResponse();

  // const message = state.messageFromUser.content.toLowerCase();

  // Check if the message contains any of the supported channels
  // const supportedChannels = [
  //   "travelxp",
  //   "protv",
  //   "fast&fun box",
  //   "fight box",
  //   "tv5",
  //   "a2z",
  //   "film box",
  // ];

  // return supportedChannels.some((channel) =>
  //   message.includes(channel.toLowerCase()),
  // );

  // let lambdaResponse = await invokeLambda("tvScheduleFetcher", "REQUEST_RESPONSE",
  //     {
  //       queryStringParameters: {
  //         q: "What's on TravelXP for the next 3 hours?",
  //       },
  // });

  // D.log({ message: "Response from tvScheduleFetcher lambda", data: lambdaResponse });

  // // Parse the result
  //   let listings;
  //   try {
  //     if (typeof lambdaResponse.body === "string") {
  //       listings = JSON.parse(lambdaResponse.body);
  //     } else {
  //       listings = lambdaResponse.body;
  //     }
  //   } catch (e) {
  //     D.log({ message: "Error parsing Lambda result", error: e.message });
  //     "Sorry, I encountered an error processing the TV listings.".sendResponse();
  //     return;
  //   }

  //   D.log({ message: "Parsed listings", listings: listings });

  //   // Create a document to display the TV listings
  //   const listingsDoc = new Doc("tvListingsDoc", state, {
  //     title: "TV Listings",
  //     autoSave: false,
  //     readOnly: true,
  //   });

  //   // Add a text field to show the query
  //   const queryField = new Field("queryField", {
  //     title: "Your Request",
  //     doc: listingsDoc,
  //     type: FormFieldTypes.TEXT_FIELD,
  //     value: "What's on TravelXP for the next 3 hours?",
  //     readOnly: true,
  //     column: 0,
  //     state,
  //   });

  //   // Add the listings as a text field
  //   const listingsField = new Field("listingsField", {
  //     title: "Upcoming Shows",
  //     doc: listingsDoc,
  //     type: FormFieldTypes.TEXT_FIELD,
  //     value: listings.result || "No listings found for this time period.",
  //     readOnly: true,
  //     column: 0,
  //     state,
  //   });

  //   // Send the document with the listings
  //   listingsDoc.sendResponse();
};

// Create an intent to handle TV listing requests
export let tvListingIntent = new Intent("tvListingIntent", state);

tvListingIntent.runOnCloud(); // Run this intent on cloud
tvListingIntent.onMatching = () => {
  D.log({ message: "TV Listing Intent Matched", data: state.messageFromUser });
  return state.messageFromUser === "start";
};

// This intent will match any message that might be asking about TV listings
// tvListingIntent.onMatching = () => {
//   // Check if there's a message from the user
//   if (!state.messageFromUser || !state.messageFromUser.content) {
//     return false;
//   }

// };

tvListingIntent.onResolution = async () => {
  try {
    // Let the user know we're processing their request
    "Looking up TV listings for you...".sendResponse();
    // Get the user's query
    const userQuery = "What's on TravelXP for the next 3 hours?";
    D.log({ message: "Calling Lambda with query", data: userQuery });

    // Call the AWS Lambda function using frontmlib
    const lambdaResult = await invokeLambda(
      "tvScheduleFetcher",
      "RequestResponse",
      {
        queryStringParameters: {
          q: userQuery,
        },
      },
    );

    // const lambdaResult = await frontmlib.invokeLambda(
    //   "tvScheduleFetcher", // Replace with your actual Lambda function name
    //   frontmlib.LAMBDA_INVOCATION_TYPES.REQUEST_RESPONSE,
    //   {
    //     queryStringParameters: {
    //       q: userQuery,
    //     },
    //   },
    // );

    D.log({ message: "Lambda result", data: lambdaResult });

    // Parse the complex nested JSON response from Lambda
    let listings;
    try {
      D.log({ message: "Raw Lambda result", data: lambdaResult });

      // Step 1: Check if we have a Payload property (AWS Lambda response format)
      let responseData = lambdaResult;
      if (lambdaResult.Payload) {
        // Parse the Payload string
        responseData = JSON.parse(lambdaResult.Payload);
        D.log({ message: "Parsed Payload", data: responseData });
      }

      // Step 2: Check if the response has the expected structure
      if (!responseData || responseData.statusCode !== 200) {
        const errorMessage = responseData?.body || "No results found";
        D.log({ message: "Invalid Lambda response", data: errorMessage });
        `Sorry, I couldn't find any TV listings. ${errorMessage}`.sendResponse();
        return;
      }

      // Step 3: Parse the body if it's a string
      let bodyData = responseData;
      if (responseData.body && typeof responseData.body === "string") {
        bodyData = JSON.parse(responseData.body);
        D.log({ message: "Parsed body", data: bodyData });
      } else if (responseData.body) {
        bodyData = responseData.body;
      }

      // Step 4: Get the final result
      listings = bodyData;

      // Step 5: If the result contains escaped newlines, clean them up for display
      if (listings.result && typeof listings.result === "string") {
        listings.result = listings.result.replace(/\\n/g, "\n");
        D.log({ message: "Cleaned up result", data: listings.result });
      }
    } catch (e) {
      D.log({
        message: "Error parsing Lambda result",
        error: e.message,
        stack: e.stack,
      });
      "Sorry, I encountered an error processing the TV listings.".sendResponse();
      return;
    }

    D.log({ message: "Parsed listings", listings: listings });

    // Format the listings for better display
    let formattedListings = "No TV listings found for this time period.";
    if (listings.result) {
      // Split the result by newlines and format each listing
      const listings_lines = listings.result
        .split("\n")
        .filter((line) => line.trim()) // Remove empty lines
        .map((line) => `• ${line.trim()}`); // Add bullet points and clean up whitespace

      formattedListings = listings_lines.join("\n");
    }

    // Create a document to display the TV listings
    const listingsDoc = new Doc("tvListingsDoc", state, {
      title: "TV Listings",
      autoSave: false,
      readOnly: true,
    });

    // Add a text field to show the query
    const queryField = new Field("queryField", {
      title: "Your Request",
      doc: listingsDoc,
      type: FormFieldTypes.TEXT_FIELD,
      value: userQuery,
      readOnly: true,
      column: 0,
      state,
    });

    // Add the listings as a text field
    const listingsField = new Field("listingsField", {
      title: "TV Listings",
      doc: listingsDoc,
      type: FormFieldTypes.TEXT_FIELD,
      value: formattedListings,
      readOnly: true,
      column: 0,
      state,
    });

    D.log({ message: "Sending listings document response" });

    // Send the document with the listings
    listingsDoc.sendResponse();
  } catch (error) {
    D.log({ message: "Error fetching TV listings", data: error.message });
    "Sorry, I encountered an error while fetching TV listings. Please try again later.".sendResponse();
  }
};

// Create a fallback intent for messages that don't match other intents
export let fallbackIntent = new Intent("fallbackIntent", state);

fallbackIntent.runOnCloud(); // Run this intent on cloud

fallbackIntent.onMatching = () =>
  state.messageFromUser.intentId === "fallbackIntent";

fallbackIntent.onResolution = async () => {
  D.log({ message: "Fallback intent resolved" });
  "I can show you TV listings for specific channels. Try asking something like 'What's on TravelXP for the next 3 hours?'".sendResponse();
  "Supported channels: TravelXP, ProTV, Fast&Fun Box, Fight Box, TV5, A2Z, Film Box".sendResponse();
};
