import { D, state } from "@frontmltd/frontmjs/core/State";
import { Intent } from "@frontmltd/frontmjs/core/Intent";
import { SYSTEM_INTENTS } from "@frontmltd/frontmjs/core/ALLConstants";
import { Field } from "@frontmltd/frontmjs/core/Field";
import { FormFieldTypes } from "@frontmltd/frontmjs/core/FormFieldTypes";
import { Doc } from "@frontmltd/frontmjs/core/Doc";

export async function invokeLambda(functionName, invocationType, jsonPayload) {
  try {
    D.log({ message: `Invoking Lambda: ${functionName}` });
    return await state.callLambda(functionName, jsonPayload, invocationType);
    // return await frontmlib.invokeLambda(
    //   functionName, // Replace with your actual Lambda function name
    //   invocationType,
    //   jsonPayload
    // );
  } catch (error) {
    console.error(
      `Error calling lambda ${functionName} with error object ${error}`,
    );
  }
}

// Create the main intent for initial greeting
export let main = new Intent(SYSTEM_INTENTS.MAIN, state);

main.runOnCloud();

main.onMatching = () => {
  D.log({ message: "Main intent matched", data: state.messageFromUser });
  state.messageFromUser.intentId === SYSTEM_INTENTS.MAIN;
};

main.onResolution = async () => {
  D.log({ message: "Main intent resolved" });

  "Welcome to the TV Guide Bot! You can ask me about TV listings by typing something like 'What's on TravelXP for the next 3 hours?'".sendResponse();
  "Supported channels: TravelXP, ProTV, Fast&Fun Box, Fight Box, TV5, A2Z, Film Box".sendResponse();

  // const message = state.messageFromUser.content.toLowerCase();

  // Check if the message contains any of the supported channels
  // const supportedChannels = [
  //   "travelxp",
  //   "protv",
  //   "fast&fun box",
  //   "fight box",
  //   "tv5",
  //   "a2z",
  //   "film box",
  // ];

  // return supportedChannels.some((channel) =>
  //   message.includes(channel.toLowerCase()),
  // );

  // let lambdaResponse = await invokeLambda("tvScheduleFetcher", "REQUEST_RESPONSE",
  //     {
  //       queryStringParameters: {
  //         q: "What's on TravelXP for the next 3 hours?",
  //       },
  // });

  // D.log({ message: "Response from tvScheduleFetcher lambda", data: lambdaResponse });

  // // Parse the result
  //   let listings;
  //   try {
  //     if (typeof lambdaResponse.body === "string") {
  //       listings = JSON.parse(lambdaResponse.body);
  //     } else {
  //       listings = lambdaResponse.body;
  //     }
  //   } catch (e) {
  //     D.log({ message: "Error parsing Lambda result", error: e.message });
  //     "Sorry, I encountered an error processing the TV listings.".sendResponse();
  //     return;
  //   }

  //   D.log({ message: "Parsed listings", listings: listings });

  //   // Create a document to display the TV listings
  //   const listingsDoc = new Doc("tvListingsDoc", state, {
  //     title: "TV Listings",
  //     autoSave: false,
  //     readOnly: true,
  //   });

  //   // Add a text field to show the query
  //   const queryField = new Field("queryField", {
  //     title: "Your Request",
  //     doc: listingsDoc,
  //     type: FormFieldTypes.TEXT_FIELD,
  //     value: "What's on TravelXP for the next 3 hours?",
  //     readOnly: true,
  //     column: 0,
  //     state,
  //   });

  //   // Add the listings as a text field
  //   const listingsField = new Field("listingsField", {
  //     title: "Upcoming Shows",
  //     doc: listingsDoc,
  //     type: FormFieldTypes.TEXT_FIELD,
  //     value: listings.result || "No listings found for this time period.",
  //     readOnly: true,
  //     column: 0,
  //     state,
  //   });

  //   // Send the document with the listings
  //   listingsDoc.sendResponse();
};

// Create an intent to handle TV listing requests
export let tvListingIntent = new Intent("tvListingIntent", state);

tvListingIntent.runOnCloud(); // Run this intent on cloud

// Store the user message during matching when it might be available
let storedUserMessage = null;

// Try a more persistent global approach
if (typeof global !== "undefined") {
  global.currentUserQuery = null;
}

tvListingIntent.onMatching = () => {
  D.log({ message: "TV Listing Intent Matched", data: state.messageFromUser });

  // Try to capture the user message during matching from multiple sources
  let capturedMessage = null;

  // Try different ways to access the message
  if (state.messageFromUser?.content) {
    capturedMessage = state.messageFromUser.content;
  } else if (state.messageFromUser?.text) {
    capturedMessage = state.messageFromUser.text;
  } else if (state.messageFromUser?.message) {
    capturedMessage = state.messageFromUser.message;
  }

  if (capturedMessage) {
    storedUserMessage = capturedMessage;
    if (typeof global !== "undefined") {
      global.currentUserQuery = capturedMessage;
    }
    D.log({
      message: "Successfully captured user message during matching",
      stored: storedUserMessage,
      global: global?.currentUserQuery,
    });
  } else {
    D.log({ message: "Could not capture user message during matching" });
  }

  return state.messageFromUser?.intentId !== SYSTEM_INTENTS.MAIN;
};

// This intent will match any message that might be asking about TV listings
// tvListingIntent.onMatching = () => {
//   // Check if there's a message from the user
//   if (!state.messageFromUser || !state.messageFromUser.content) {
//     return false;
//   }

// };

tvListingIntent.onResolution = async (params) => {
  try {
    // Let the user know we're processing their request
    "Looking up TV listings for you...".sendResponse();

    // Debug: Check what's available in different places
    D.log({ message: "Resolution params", data: params });
    D.log({ message: "Message state debug", data: state.messageFromUser });
    D.log({ message: "Full state object", data: state });
    D.log({ message: "State keys", data: Object.keys(state) });

    // Check if there are other message-related properties
    if (state["message"])
      D.log({ message: "state.message", data: state["message"] });
    if (state["userMessage"])
      D.log({ message: "state.userMessage", data: state["userMessage"] });
    if (state["lastMessage"])
      D.log({ message: "state.lastMessage", data: state["lastMessage"] });
    if (state["currentMessage"])
      D.log({ message: "state.currentMessage", data: state["currentMessage"] });
    if (state["input"]) D.log({ message: "state.input", data: state["input"] });
    if (state["userInput"])
      D.log({ message: "state.userInput", data: state["userInput"] });

    // Try to get the user query from multiple sources
    let userQuery = null; // No fallback - force us to find the real message

    // Add a small delay and try to access the message again
    await new Promise((resolve) => setTimeout(resolve, 100)); // 100ms delay

    D.log({
      message: "After delay - checking message again",
      data: state.messageFromUser,
    });

    // Try to access the message through different paths
    const messageContent =
      state.messageFromUser?.content ||
      state.messageFromUser?.text ||
      state.messageFromUser?.message ||
      state["lastUserMessage"] ||
      state["currentUserMessage"];

    D.log({ message: "Extracted message content", content: messageContent });

    // First try the global variable
    if (typeof global !== "undefined" && global.currentUserQuery) {
      userQuery = global.currentUserQuery;
      D.log({ message: "Using global stored query", content: userQuery });
    }
    // Then try the extracted message content
    else if (messageContent) {
      userQuery = messageContent;
      D.log({ message: "Using extracted message content", content: userQuery });
    }
    // Then try the stored message from matching phase
    else if (storedUserMessage) {
      userQuery = storedUserMessage;
      D.log({
        message: "Using stored message from matching",
        stored: storedUserMessage,
      });
    }
    // Try accessing the message content after delay
    else if (state.messageFromUser?.content) {
      userQuery = state.messageFromUser.content;
      D.log({
        message: "Using message content after delay",
        content: userQuery,
      });
    }
    // Try getting the latest message from conversation history with different approaches
    else {
      try {
        // Try getting recent messages with different parameters
        D.log({
          message: "Attempting to get conversation history",
          conversationId: state.conversationId,
        });

        const recentMessages = await frontmlib.getMessages(
          state.conversationId,
          Date.now(),
          5, // Get more messages to find the user's message
          -1, // Sort descending (newest first)
          "OLDER", // Get older messages
        );

        D.log({
          message: "Retrieved messages from history",
          messages: recentMessages,
        });

        if (recentMessages && recentMessages.length > 0) {
          // Find the most recent user message (not from bot)
          const userMessage = recentMessages.find(
            (msg) =>
              msg.content &&
              msg.createdBy !== "bot" &&
              msg.createdBy !== state.conversationId && // Not system messages
              typeof msg.content === "string" &&
              msg.content.toLowerCase().includes("protv"), // Look specifically for ProTV
          );

          if (userMessage) {
            userQuery = userMessage.content;
            D.log({
              message: "Found ProTV message in conversation history",
              content: userQuery,
              messageDetails: userMessage,
            });
          } else {
            // Fallback: use the most recent non-bot message
            const latestUserMessage = recentMessages.find(
              (msg) => msg.content && msg.createdBy !== "bot",
            );
            if (latestUserMessage) {
              userQuery = latestUserMessage.content;
              D.log({
                message: "Using latest user message from history",
                content: userQuery,
              });
            }
          }
        }
      } catch (error) {
        D.log({
          message: "Error getting conversation history",
          error: error.message,
        });
      }
    }

    D.log({ message: "Final user query", query: userQuery });

    // If we still don't have a user query, provide a helpful response
    if (!userQuery) {
      D.log({
        message: "ERROR: No user query found through any method!",
      });
      "I'm having trouble understanding your request. Please try asking about TV listings like 'What's on ProTV?' or 'Show me TravelXP schedule'.".sendResponse();
      return;
    }
    D.log({ message: "Calling Lambda with query", data: userQuery });

    // Call the AWS Lambda function using frontmlib
    const lambdaResult = await invokeLambda(
      "tvScheduleFetcher",
      "RequestResponse",
      {
        queryStringParameters: {
          q: userQuery,
        },
      },
    );

    // const lambdaResult = await frontmlib.invokeLambda(
    //   "tvScheduleFetcher", // Replace with your actual Lambda function name
    //   frontmlib.LAMBDA_INVOCATION_TYPES.REQUEST_RESPONSE,
    //   {
    //     queryStringParameters: {
    //       q: userQuery,
    //     },
    //   },
    // );

    D.log({ message: "Lambda result", data: lambdaResult });

    // Parse the complex nested JSON response from Lambda
    let listings;
    try {
      D.log({ message: "Raw Lambda result", data: lambdaResult });

      // Step 1: Check if we have a Payload property (AWS Lambda response format)
      let responseData = lambdaResult;
      if (lambdaResult.Payload) {
        // Parse the Payload string
        responseData = JSON.parse(lambdaResult.Payload);
        D.log({ message: "Parsed Payload", data: responseData });
      }

      // Step 2: Check if the response has the expected structure
      if (!responseData || responseData.statusCode !== 200) {
        const errorMessage = responseData?.body || "No results found";
        D.log({ message: "Invalid Lambda response", data: errorMessage });
        `Sorry, I couldn't find any TV listings. ${errorMessage}`.sendResponse();
        return;
      }

      // Step 3: Parse the body if it's a string
      let bodyData = responseData;
      if (responseData.body && typeof responseData.body === "string") {
        bodyData = JSON.parse(responseData.body);
        D.log({ message: "Parsed body", data: bodyData });
      } else if (responseData.body) {
        bodyData = responseData.body;
      }

      // Step 4: Get the final result
      listings = bodyData;

      // Step 5: If the result contains escaped newlines, clean them up for display
      if (listings.result && typeof listings.result === "string") {
        listings.result = listings.result.replace(/\\n/g, "\n");
        D.log({ message: "Cleaned up result", data: listings.result });
      }
    } catch (e) {
      D.log({
        message: "Error parsing Lambda result",
        error: e.message,
        stack: e.stack,
      });
      "Sorry, I encountered an error processing the TV listings.".sendResponse();
      return;
    }

    D.log({ message: "Parsed listings", listings: listings });

    // Display the TV listings
    if (listings.result) {
      // Split the result by newlines and format each listing
      const listings_lines = listings.result
        .split("\n")
        .filter((line) => line.trim()) // Remove empty lines
        .map((line) => line.trim()); // Clean up whitespace

      D.log({ message: "Formatted listings lines", data: listings_lines });

      // Create a single formatted message with HTML line breaks
      const numberedListings = listings_lines.map(
        (line, index) => `${index + 1}. ${line}`,
      );
      const htmlFormattedMessage = `Here are the TV listings:<br><br>${numberedListings.join("<br>")}`;

      // Send as a single HTML-formatted message
      htmlFormattedMessage.sendResponse();
    } else {
      "No TV listings found for this time period.".sendResponse();
    }
  } catch (error) {
    D.log({ message: "Error fetching TV listings", data: error.message });
    "Sorry, I encountered an error while fetching TV listings. Please try again later.".sendResponse();
  }
};

// Create a fallback intent for messages that don't match other intents
export let fallbackIntent = new Intent("fallbackIntent", state);

fallbackIntent.runOnCloud(); // Run this intent on cloud

fallbackIntent.onMatching = () =>
  state.messageFromUser.intentId === "fallbackIntent";

fallbackIntent.onResolution = async () => {
  D.log({ message: "Fallback intent resolved" });
  "I can show you TV listings for specific channels. Try asking something like 'What's on TravelXP for the next 3 hours?'".sendResponse();
  "Supported channels: TravelXP, ProTV, Fast&Fun Box, Fight Box, TV5, A2Z, Film Box".sendResponse();
};
